<div class="relative overflow-hidden <%= @background_color %> <%= @gradient_to_color %> bg-gradient-to-b py-16 px-4 lg:px-6 mb-16">
  <!-- Subtle Background Elements -->
  <div class="absolute inset-0">
    <!-- Grid <PERSON>tern -->
    <div class="absolute inset-0 opacity-5">
      <div class="grid grid-cols-12 gap-4 h-full">
        <% (1..12).each do |i| %>
          <div class="border-r border-white/20"></div>
        <% end %>
      </div>
    </div>
  </div>

  <div class="relative gap-8 <%= @image_inline ? "items-center" : "items-center" %> mx-auto max-w-screen-xl xl:gap-16 grid md:grid-cols-2">
    <div class="<%= @text_color %>">
      <% if helpers.breadcrumbs_any? %>
        <div class="mb-4">
          <%= helpers.render_breadcrumbs(omit_text_color: true) %>
        </div>
      <% end %>
      
      <div class="mb-6">
        <h1 class="text-3xl sm:text-5xl mb-4 font-black bg-clip-text text-transparent bg-gradient-to-r from-white via-gray-100 to-white drop-shadow-sm"><%= @title %></h1>
        <%- if @subtitle.present? %>
          <p class="font-semibold text-xl mb-4 text-gray-100/90"><%= @subtitle %></p>
        <%- end %>
      </div>
      
      <%- if @body.present? %>
        <div class="mb-8">
          <%- if @body_2.present? %>
            <p class="sm:text-lg mb-3 text-gray-200/90 leading-relaxed"><%= @body %></p>
            <p class="sm:text-lg text-gray-200/90 leading-relaxed"><%= @body_2 %></p>
          <%- else %>
            <p class="sm:text-lg text-gray-200/90 leading-relaxed"><%= @body %></p>
          <%- end %>
        </div>
      <%- end %>
      
      <%- if @primary_action %>
        <div class="flex flex-wrap gap-4">
          <%= link_to @primary_action[:path], 
              class: "group relative inline-flex items-center px-8 py-4 bg-gradient-to-r from-lime-500 via-green-500 to-emerald-500 hover:from-lime-400 hover:via-green-400 hover:to-emerald-400 text-white font-bold rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-lime-500/25 transform" do %>
            <!-- Subtle Glow Effect -->
            <div class="absolute inset-0 bg-gradient-to-r from-lime-500 to-emerald-500 rounded-xl blur opacity-30 group-hover:opacity-50 transition-opacity duration-300"></div>
            <span class="relative flex items-center">
              <%= @primary_action[:text] %>
              <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform duration-300"></i>
            </span>
          <% end %>
          
          <%- if @secondary_action %>
            <%= link_to @secondary_action[:path], 
                class: "group relative inline-flex items-center px-6 py-4 bg-white/10 backdrop-blur-sm border border-white/30 text-white font-medium rounded-xl hover:bg-white/20 hover:border-white/50 transition-all duration-300 hover:scale-105" do %>
              <span class="relative flex items-center">
                <%= @secondary_action[:text] %>
                <i class="fas fa-chevron-right ml-2 text-sm group-hover:translate-x-1 transition-transform duration-300"></i>
              </span>
            <% end %>
          <%- end %>
        </div>
      <% end %>
    </div>
    
    <div class="hidden md:block">
      <%- if @image %>
        <div class="relative flex justify-center items-center">
          <%= image_tag @image, 
              class: "max-w-full h-auto transform hover:scale-105 transition-transform duration-500 drop-shadow-2xl", 
              style: @max_image_width ? "max-width: #{@max_image_width}px" : nil, 
              alt: @alt_text %>
        </div>
      <%- end %>
    </div>
  </div>
</div>