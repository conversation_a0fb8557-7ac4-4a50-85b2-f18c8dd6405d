# frozen_string_literal: true

class FullWidthBannerComponent < ViewComponent::Base
  BACKGROUND_THEMES = {
    blue: {
      background_color: 'from-cyan-600 via-blue-600',
      gradient_to_color: 'to-indigo-700'
    },
    purple: {
      background_color: 'from-purple-600 via-violet-600',
      gradient_to_color: 'to-indigo-700'
    },
    green: {
      background_color: 'from-teal-600 via-emerald-600',
      gradient_to_color: 'to-cyan-600'
    },
    orange: {
      background_color: 'from-amber-600 via-orange-600',
      gradient_to_color: 'to-yellow-600'
    },
    gray: {
      background_color: 'from-slate-600 via-gray-600',
      gradient_to_color: 'to-zinc-700'
    }
  }.freeze

  def initialize(title:, subtitle: nil, body: nil, body_2: nil, image: nil, alt_text: nil, theme: :blue, text_color: 'text-white', primary_action: nil, secondary_action: nil, max_image_width: 500, image_inline: false)
    @title = title
    @subtitle = subtitle
    @body = body
    @body_2 = body_2
    @image = image
    @alt_text = alt_text.nil? ? title : alt_text
    @theme = theme.to_sym
    @text_color = text_color
    @primary_action = primary_action
    @secondary_action = secondary_action
    @max_image_width = max_image_width
    @image_inline = image_inline == true
    
    # Set background colors based on theme
    theme_config = BACKGROUND_THEMES[@theme] || BACKGROUND_THEMES[:blue]
    @background_color = theme_config[:background_color]
    @gradient_to_color = theme_config[:gradient_to_color]
  end
end