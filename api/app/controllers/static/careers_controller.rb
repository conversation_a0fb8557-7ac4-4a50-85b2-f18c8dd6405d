module Static
  class CareersController < AnonController
    include BreadcrumbsHelper
    include PaginationHelper
    layout :dynamic_layout

    before_action :set_base_breadcrumbs

    def index
      @job_families = JobFamily.all
      @careers = CareerPath.uniq_by_name.includes(:job_family, :career).completed
      @flows = Flow.published.order(created_at: :desc)
    end

    def search
      @query = params[:query]
      @job_family_filter = params[:job_family]
      @education_level_filter = params[:education_level]
      @sort_by = params[:sort_by] || 'name'
      
      # Start with all career paths
      @careers = CareerPath.uniq_by_name.includes(:job_family, :career)
      
      # Apply search query
      if @query.present?
        @careers = @careers.where(
          "career_name ILIKE ? OR career_path::text ILIKE ?", 
          "%#{@query}%", "%#{@query}%"
        )
      end
      
      # Apply job family filter
      if @job_family_filter.present?
        @careers = @careers.where(job_family_id: @job_family_filter)
      end
      
      # Apply education level filter
      if @education_level_filter.present?
        @careers = @careers.where(education_level: @education_level_filter)
      end
      
      # Apply sorting
      case @sort_by
      when 'name'
        @careers = @careers.order(:career_name)
      when 'recent'
        @careers = @careers.order(created_at: :desc)
      when 'job_family'
        @careers = @careers.joins(:job_family).order('job_families.name', :career_name)
      else
        @careers = @careers.order(:career_name)
      end
      
      # Only show completed careers
      @careers = @careers.where(status: 'completed')
      
      # Pagination
      @careers = safe_paginate(@careers, page: params[:page], per_page: 12)
      
      # Data for filters
      @job_families = JobFamily.all.order(:name)
      @education_levels = CareerPath.distinct.pluck(:education_level).compact.sort
      
      # Search stats
      @total_results = @careers.count
      
      add_breadcrumb "Search", careers_search_path
    end

    def favourites
      # Get user's favourite career paths
      @user_career_paths = current_user.user_career_paths
                                      .where(is_favourite: true)
                                      .includes(career_path: [:job_family, :career])
      
      # Only include completed career paths
      @user_career_paths = @user_career_paths.joins(:career_path).where(career_paths: { status: 'completed' })
      
      # Pagination
      @user_career_paths = @user_career_paths.paginate(page: params[:page], per_page: 12)
      
      # Stats
      @total_favourites = @user_career_paths.total_entries
      
      add_breadcrumb "Favourites", careers_favourites_path
    end

    private

    def set_base_breadcrumbs
      add_root_breadcrumb(current_user)
      add_breadcrumb "Careers", careers_index_path
    end

    def dynamic_layout
      if @current_user&.pupil?
        'pupil'
      elsif @current_user&.teacher?
        'school'
      else
        'static'
      end
    end
  end
end