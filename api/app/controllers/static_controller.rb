class StaticController < StaticApplicationController
  include BreadcrumbsHelper

  skip_before_action :verify_authenticity_token, only: [:video_view]
  before_action :set_base_breadcrumbs, only: [:approach]

  def home
    @social_posts = custom_cache.fetch("home_social_posts", expires_in: 15.minutes) do
      DeSocial.joins(:de_media)
              .where(published: true)
              .group('de_socials.id')
              .order(created_at: :desc)
              .limit(3)
              .select('de_socials.*, ARRAY_AGG(de_media.fileboy_image_id) AS fileboy_image_ids')
    end
    @blog_posts = custom_cache.fetch("home_blog_posts", expires_in: 15.minutes) do
      Article.ordered.featured
    end
    @accreditations = custom_cache.fetch("home_accreditations", expires_in: 15.minutes) do
      Accreditation.where.not(fileboy_image_id: nil).all
    end
    @featured_units = custom_cache.fetch("home_featured_units", expires_in: 15.minutes) do
      NewLibrary::Unit.includes([campaign_units: [campaign: :organisation]]).featured_on_homepage
    end
    @sign_up_stats = custom_cache.fetch("home_sign_up_stats", expires_in: 1.minutes) do
      count = IndividualUser.where("created_at > ?", 7.days.ago).count + School.where("created_at > ?", 7.days.ago).count
      rounded_count = count > 50 ? (count / 10) * 10 : 50
      { 
        last_7_days: rounded_count,
        last_sign_up: {
          time: School.generic.order(created_at: :desc).first&.created_at,
          location: School.generic.order(created_at: :desc).first&.postcode&.first(3),
        }
      }
    end
    @sponsors = custom_cache.fetch("sponsor-organisations", expires_in: 10.minutes) do
      Organisation.where(is_sponsor: true)
    end
  end

  def schools
    @featured_units = NewLibrary::Unit.featured_on_homepage
  end

  def cookie_policy
  end

  def privacy_policy
  end

  def terms_and_conditions
  end

  def research
  end

  def careers_ai; end

  def lessons_ai; end

  def science_subject; end

  def geography_subject; end

  def plans_and_pricing
    @school_type = params[:type].present? ? params[:type] : 'school'
    price = '-'
    discount_percent = 0
    
    if params[:payment_type].present? && params[:pupils].present? && params[:year].present? && params[:subject].present?
      # The selected subjects (as an array of strings like ["science", "ai", "geography"])
      subjects = params[:subject].reject(&:blank?)
      
      prices = StripeProducts::BASE_PRICES
      base_price = prices[params[:pupils].to_sym] || 0

      # Calculate total base price (base price × number of products)
      total_base_price = base_price * subjects.length
      
      # Apply discount based on number of products
      discount = case subjects.length
                 when 2
                  discount_percent = 10
                   0.10  # 10% discount for 2 products
                 when 3
                  discount_percent = 20
                   0.20  # 20% discount for 3 products
                 else
                   0     # No discount for 1 product
                 end
      
      # Apply discount to get final price
      price = total_base_price * (1 - discount)
    end

    respond_to do |format|
      format.html
      format.json { render json: { price: price, discount: discount_percent } }
    end
  end

  def product
  end

  def reporting
  end

  def legal
  end

  def gdpr
  end

  def enquiry
  end

  def careers_info
  end

  def affiliates
  end

  def vacancy_index
    @job_listings = JobListing.accessible_by(current_ability).order(end_date: :desc, created_at: :desc)
    if params[:scope] == "active"
      @job_listings = @job_listings.active
    end
  end

  def vacancy_show
    @job_listing = JobListing.find_by(id: params[:id])

    # if the job doesnt exist, render a 404 page
    raise ActiveRecord::RecordNotFound if @job_listing.nil?

    # if the job exists, but is not published, render a specific page to indicate this.
    unless @job_listing.published?
      @job_listing = nil
      render 'static/career_not_found'
      return
    end

    file_name = ["developing_experts", @job_listing&.title&.downcase&.parameterize || ""].join("_")
    @pdf = @job_listing&.fileboy_pdf_id.present? ? {
      name: file_name,
      id: @job_listing&.fileboy_pdf_id
    } : nil
    @application_form = @job_listing&.fileboy_application_form_id.present? ? {
      name: [file_name, "application"].join("_"),
      id: @job_listing&.fileboy_application_form_id
    } : nil
  end

  def about_us
    team_members = TeamMember.all
    @board_members = team_members.select(&:board?)
    @staff_members = team_members.select(&:staff?)
    @expert_members = team_members.select(&:expert?)
  end

  def childrens_code
  end

  def footer_subscribe
    render layout: false
  end

  def video
    @video = Video.find_by(id: params[:id])
    @autoPlay = params[:autoPlay].present? && params[:autoPlay] == "1"
    if @video.nil?
      head :not_found
    else
      render :layout => false
    end
  end

  def video_view
    @video = Video.find_by(id: params[:id])
    if @video.nil?
      head :not_found
    else
      TrackingService.track_video_view(@video, current_user)
      head :ok
    end
  end

  def logout
    current_user.device_logins.destroy_all if current_user
    sign_out
    render 'static/logout'
  end
  
  def unavailable_region
  end

  def approach
  end

  def send_page
  end

  def marketing_link
    marketing_identifier = params[:marketing_identifier]
    encoded_link_destination = params[:link_destination]
    
    if marketing_identifier.present? && encoded_link_destination.present?
      # Decode the URL-encoded destination
      link_destination = CGI.unescape(encoded_link_destination)
      
      # Track the click
      TrackingService.track_marketing_link_click(marketing_identifier, link_destination)
      
      # Redirect to the decoded destination
      redirect_to link_destination
    else
      head :bad_request
    end
  end

  private

  def set_base_breadcrumbs
    add_breadcrumb "Home", root_path
    add_breadcrumb action_name.titleize
  end
end