<% content_for :title, "#{@job_family.name} | Job Family" %>
<% card_colours = [
  { bg: "#a869a1", text: "#1c1f42" },
  { bg: "#77b797", text: "#1c1f42" },
  { bg: "#d3666f" },
  { bg: "#3d91cd", text: "#1c1f42" },
  { bg: "#f7d277", text: "#1c1f42" },
  { bg: "#387b92" },
  { bg: "#99a53f", text: "#1c1f42" },
  { bg: "#89264b" },
  { bg: "#e69535", text: "#1c1f42" }
] %>
<% sections = [
  { id: "myths-facts", label: "🎭 Myths & Facts" },
  { id: "careers", label: "🚀 Careers" },
  { id: "skills", label: "🧠 Skills" },
  { id: "industries", label: "🏭 Industries" },
  { id: "workplaces", label: "🗺️ Workplaces" }
] %>

<div class="min-h-screen bg-gray-900">
  <!-- Subtle Banner Section -->
  <div class="relative overflow-hidden bg-gradient-to-r from-purple-900/70 via-blue-900/70 to-cyan-900/70 py-12">
    <!-- Subtle Background Elements -->
    <div class="absolute inset-0">
      <!-- Grid Pattern -->
      <div class="absolute inset-0 opacity-3">
        <div class="grid grid-cols-12 gap-4 h-full">
          <% (1..12).each do |i| %>
            <div class="border-r border-white/10"></div>
          <% end %>
        </div>
      </div>
      
      <!-- Minimal Floating Icons -->
      <div class="absolute top-8 right-1/4 text-cyan-300/20 text-xl">
        <i class="fas fa-users"></i>
      </div>
      <div class="absolute bottom-8 left-1/3 text-purple-300/20 text-lg">
        <i class="fas fa-sitemap"></i>
      </div>
    </div>
    
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
        <!-- Left Content -->
        <div class="lg:col-span-2">
          <!-- Breadcrumbs -->
          <div class="mb-4">
            <%= render_breadcrumbs(dark_text: false) %>
          </div>
          
          <h1 class="text-4xl md:text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-300 to-purple-300 mb-4">
            <%= @job_family.name %>
          </h1>
          
          <% if @job_family.description.present? %>
            <p class="text-lg text-gray-200 leading-relaxed">
              <%= @job_family.description %>
            </p>
          <% end %>
        </div>
        
        <!-- Right Content - Job Family Image -->
        <div class="relative">
          <div class="relative group">
            <div class="relative overflow-hidden rounded-2xl transform group-hover:scale-105 transition-transform duration-500">
              <% if @job_family.fileboy_image_id.present? %>
                <% image_url = "https://www.developingexperts.com/file-cdn/images/get/#{@job_family.fileboy_image_id}?transform=resize:600x400~fit:cover;format:webp;quality:75" %>
                <%= image_tag image_url, 
                    alt: @job_family.name,
                    class: "w-full h-64 object-cover" %>
              <% else %>
                <div class="w-full h-64 bg-gradient-to-br from-gray-700 to-gray-900 flex flex-col items-center justify-center">
                  <i class="fas fa-briefcase text-6xl text-gray-500 mb-4"></i>
                  <span class="text-gray-400 text-lg font-medium">Job Family</span>
                </div>
              <% end %>
              
              <!-- Gradient Overlay -->
              <div class="absolute inset-0 bg-gradient-to-tr from-purple-600/20 via-transparent to-cyan-400/20"></div>
              
              <!-- Floating Stats -->
              <% if @job_family.career_paths.any? %>
                <div class="absolute top-4 right-4 bg-white/10 backdrop-blur-md rounded-lg p-3 border border-white/20">
                  <div class="text-2xl font-black text-cyan-300"><%= @job_family.career_paths.count %></div>
                  <div class="text-xs text-white font-semibold">Careers</div>
                </div>
              <% end %>
              
            </div>
            
            <!-- Decorative Elements -->
            <div class="absolute -top-2 -right-2 w-6 h-6 bg-cyan-400 rounded-full animate-pulse"></div>
            <div class="absolute -bottom-2 -left-2 w-4 h-4 bg-purple-400 rounded-full animate-pulse delay-500"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="flex flex-col lg:flex-row max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-12 space-y-8 lg:space-y-0 lg:space-x-10 pb-16">

    <!-- Sidebar Navigation -->
    <nav class="hidden lg:block sticky top-8 h-max w-60 bg-gray-800 border border-gray-700 rounded-xl p-4 text-white">
      <h2 class="text-xl font-bold mb-4 text-transparent bg-clip-text bg-gradient-to-r from-cyan-300 to-purple-300">
        Explore
      </h2>
      <ul class="space-y-2 text-sm">
        <% sections.each do |section| %>
          <li>
            <a href="<%= "##{section[:id]}" %>" class="hover:text-cyan-400 block py-2 px-3 rounded-lg hover:bg-gray-700/50 transition-all duration-300">
              <%= section[:label] %>
            </a>
          </li>
        <% end %>
      </ul>
    </nav>

    <!-- Main Content -->
    <div class="flex-1 space-y-12 text-white">

      <!-- Virtual Work Experience -->
      <% if @flows&.any? %>
        <%= render 'shared/flow_showcase',
            flows: @flows,
            title: "Virtual Work Experience",
            show_all_link: { path: "/courses?job_family_id=#{@job_family.id}", text: "View All Experiences" } %>
      <% end %>

      <!-- Myths & Facts -->
      <section id="myths-facts">
        <h2 class="text-3xl font-bold mb-8 text-transparent bg-clip-text bg-gradient-to-r from-cyan-300 to-purple-300">
          🎭 Myths & Facts
        </h2>
        <div class="flip-card-container">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div class="flip-card-nohover bg-[#e69535] relative grid rounded-xl overflow-hidden hover:scale-105 transition-transform duration-300">
              <div class="place-self-center w-[90%]">
                <%= render partial: 'shared/morgan_sindall_svg_partial', locals: { svg_type: :myth_fact } %>
              </div>
              <div class="absolute top-2 right-2 w-1/3 z-0">
                <%= render partial: 'shared/morgan_sindall_svg_partial', locals: { svg_type: :drops } %>
              </div>
            </div>
            <% @job_family.myths_facts.each_with_index do |myth_fact, i| %>
              <% colour = card_colours[i % card_colours.length] %>
              <% bg_colour = colour[:bg] %>
              <% text_colour = colour[:text] || "#fff" %>

              <div class="flip-card bg-[<%= bg_colour %>] hover:scale-105 transition-transform duration-300" onclick="toggleContent(this)">
                <div class="flip-card-inner">
                  <div class="flip-card-front">
                    <h3 class="text-[<%= text_colour %>] font-bold">
                      <%= myth_fact["myth"] %>
                    </h3>
                  </div>
                  <div class="flip-card-back">
                    <p class="text-[<%= text_colour %>] font-medium">
                      <%= myth_fact["fact"] %>
                    </p>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </section>

      <!-- Careers -->
      <% if @job_family.career_paths.any? %>
        <section id="careers">
          <h2 class="text-3xl font-bold mb-8 text-transparent bg-clip-text bg-gradient-to-r from-cyan-300 to-purple-300">
            🚀 Careers
          </h2>
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            <% @job_family.career_paths.uniq_by_name.each do |career_path| %>
              <%= render 'static/career_builder/career_card', career: career_path %>
            <% end %>
          </div>
        </section>
      <% end %>

      <!-- Skills -->
      <% if @job_family.skills.any? %>
        <section id="skills">
          <h2 class="text-3xl font-bold mb-8 text-transparent bg-clip-text bg-gradient-to-r from-cyan-300 to-purple-300">
            🧠 Skills
          </h2>
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <% @job_family.skills.each do |skill| %>
              <div class="bg-gray-800 border border-gray-700 rounded-xl p-4 hover:border-purple-400/50 hover:scale-105 transition-all duration-300">
                <h3 class="font-semibold text-xl mb-1 text-white"><%= skill["value"] %></h3>
                <% if skill["category"].present? %>
                  <p class="text-sm text-gray-400 uppercase tracking-wide"><%= skill["category"] %></p>
                <% end %>
              </div>
            <% end %>
          </div>
        </section>
      <% end %>

      <!-- Industries -->
      <% if @job_family.industries.any? %>
        <section id="industries">
          <h2 class="text-3xl font-bold mb-8 text-transparent bg-clip-text bg-gradient-to-r from-cyan-300 to-purple-300">
            🏭 Industries
          </h2>
          <div class="flex flex-wrap gap-3">
            <% @job_family.industries.each do |industry| %>
              <span class="bg-gray-800 border border-gray-700 hover:border-purple-400/50 rounded-full px-4 py-2 text-sm text-white font-medium hover:scale-105 transition-all duration-300 cursor-default">
                <%= industry %>
              </span>
            <% end %>
          </div>
        </section>
      <% end %>

      <!-- Workplaces -->
      <% if @job_family.workplaces.any? %>
        <section id="workplaces">
          <h2 class="text-3xl font-bold mb-8 text-transparent bg-clip-text bg-gradient-to-r from-cyan-300 to-purple-300">
            🗺️ Workplaces
          </h2>
          <div class="flex flex-wrap gap-3">
            <% @job_family.workplaces.each do |place| %>
              <span class="bg-gray-800 border border-gray-700 hover:border-cyan-400/50 rounded-full px-4 py-2 text-sm text-white font-medium hover:scale-105 transition-all duration-300 cursor-default">
                <%= place %>
              </span>
            <% end %>
          </div>
        </section>
      <% end %>

      <!-- Call to Action -->
      <div class="text-center py-12">
        <div class="bg-gradient-to-r from-gray-800 to-gray-700 rounded-2xl p-8 border border-gray-600">
          <h3 class="text-2xl font-bold text-white mb-4">Interested in a Career in <%= @job_family.name %>?</h3>
          <p class="text-gray-300 mb-6">
            Use our AI-powered tool to discover personalised career paths in this field.
          </p>
          <%= link_to "/careers/generate", 
              class: "inline-flex items-center px-6 py-3 bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white font-bold rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-lg" do %>
            <i class="fas fa-robot mr-2"></i>
            Generate Career Path
          <% end %>
        </div>
      </div>

    </div>
  </div>
</div>

<!-- Flip card toggle -->
<script>
  function toggleContent(element) {
    element.querySelector('.flip-card-inner').classList.toggle('flipped');
  }
</script>