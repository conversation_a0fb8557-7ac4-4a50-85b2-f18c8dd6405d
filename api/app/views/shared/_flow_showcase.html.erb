<% if @current_user&.beta_feature_enabled?(:september_15)%>

  <%# Flow Showcase Component
    Parameters:
    - flows: Array of Flow objects to display
    - title: Section title (default: "Virtual Work Experience")
    - show_all_link: Optional link to show all flows
  %>

  <% flows ||= [] %>
  <% title ||= "Virtual Work Experience" %>

  <% if flows.any? %>
    <section class="mb-16">
      <div class="flex items-center justify-between mb-8">
        <h2 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-300 to-purple-300">
          <%= title %>
        </h2>
        <% if show_all_link.present? %>
          <%= link_to show_all_link[:path], 
              class: "inline-flex items-center px-4 py-2 bg-cyan-600/20 hover:bg-cyan-600/30 text-cyan-400 hover:text-cyan-300 font-medium rounded-lg transition-colors" do %>
            <%= show_all_link[:text] || "View All" %>
            <i class="fas fa-arrow-right ml-2"></i>
          <% end %>
        <% end %>
      </div>
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <% flows.limit(2).each do |flow| %>
          <div class="group relative bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl overflow-hidden border border-gray-700 hover:border-cyan-500/50 transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl hover:shadow-cyan-500/10">
            <!-- Background Image -->
            <% if flow.fileboy_image_id.present? %>
              <% image_url = "https://www.developingexperts.com/file-cdn/images/get/#{flow.fileboy_image_id}?transform=resize:800x400~fit:cover;format:webp;quality:75" %>
              <div class="absolute inset-0">
                <%= image_tag image_url, 
                    alt: flow.name,
                    class: "w-full h-full object-cover opacity-20 group-hover:opacity-30 transition-opacity duration-300" %>
              </div>
            <% end %>
            
            <!-- Gradient Overlay -->
            <div class="absolute inset-0 bg-gradient-to-r from-purple-900/80 via-blue-900/60 to-cyan-900/80"></div>
            
            <!-- Content -->
            <div class="relative p-8 h-full flex flex-col justify-between min-h-[280px]">
              <!-- Header -->
              <div>
                <!-- Job Family Badge -->
                <% if flow.job_family.present? %>
                  <div class="inline-flex items-center px-3 py-1 bg-white/10 backdrop-blur-sm rounded-full text-sm font-medium text-cyan-300 mb-4 border border-white/20">
                    <i class="fas fa-briefcase mr-2 text-xs"></i>
                    <%= flow.job_family.name %>
                  </div>
                <% end %>
                
                <!-- Title -->
                <h3 class="text-2xl font-bold text-white mb-3 group-hover:text-cyan-300 transition-colors duration-300">
                  <%= flow.name %>
                </h3>
                
                <!-- Description -->
                <% if flow.description.present? %>
                  <p class="text-gray-300 text-sm leading-relaxed mb-4 line-clamp-3">
                    <%= truncate(flow.description, length: 150) %>
                  </p>
                <% end %>
              </div>
              
              <!-- Footer -->
              <div class="flex items-center justify-between">
                <!-- Meta Info -->
                <div class="flex items-center space-x-4 text-sm text-gray-400">                
                  <% if flow.estimated_duration_minutes.present? && flow.estimated_duration_minutes > 0 %>
                    <div class="flex items-center">
                      <i class="fas fa-clock mr-1"></i>
                      <%= pluralize(flow.estimated_duration_minutes, 'min') %>
                    </div>
                  <% end %>
                  
                  <% if flow.total_steps > 0 %>
                    <div class="flex items-center">
                      <i class="fas fa-list mr-1"></i>
                      <%= pluralize(flow.total_steps, 'step') %>
                    </div>
                  <% end %>
                </div>
                
                <!-- CTA Button -->
                <%= link_to "/courses/#{flow.id}", 
                    class: "inline-flex items-center px-6 py-3 bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-400 hover:to-blue-500 text-white font-bold rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-lg group-hover:shadow-cyan-500/25" do %>
                  <i class="fas fa-play mr-2"></i>
                  Begin
                <% end %>
              </div>
            </div>
            
            <!-- Decorative Elements -->
            <div class="absolute top-4 right-4 w-2 h-2 bg-cyan-400 rounded-full animate-pulse opacity-60"></div>
            <div class="absolute bottom-4 left-4 w-1 h-1 bg-purple-400 rounded-full animate-pulse delay-1000 opacity-40"></div>
          </div>
        <% end %>
      </div>
      
      <!-- Show more flows if there are more than 2 -->
      <% if flows.count > 2 %>
        <div class="mt-6 text-center">
          <% if show_all_link.present? %>
            <%= link_to show_all_link[:path], 
                class: "inline-flex items-center px-6 py-3 bg-gray-800/50 hover:bg-gray-700/50 text-gray-300 hover:text-white font-medium rounded-lg border border-gray-600 hover:border-gray-500 transition-all duration-300" do %>
              View <%= pluralize(flows.count - 2, 'more experience') %>
              <i class="fas fa-arrow-right ml-2"></i>
            <% end %>
          <% else %>
            <p class="text-gray-400 text-sm">
              <%= pluralize(flows.count - 2, 'more experience') %> available
            </p>
          <% end %>
        </div>
      <% end %>
    </section>
  <% end %>

  <style>
    .line-clamp-3 {
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  </style>
<% end %>