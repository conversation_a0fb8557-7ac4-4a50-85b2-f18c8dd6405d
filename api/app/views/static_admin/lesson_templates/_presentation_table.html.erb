<% hide_ai = local_assigns[:hide_ai] %>
<% limit_video_types = local_assigns.fetch(:limit_video_types, [:youtube, :vimeo, :fileboy]) %>
<%# Include Sortable.js from CDN directly in the view %>
<%= javascript_include_tag 'https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js' %>
<%- ai_generation_path = generate_with_ai_admin_lesson_template_path(@lesson_template) %>
<%# Helper for slide type specific styling %>
<%
def slide_type_styles(slide_type)
  case slide_type.to_s
  when 'text'
    { tag_class: "bg-sky-100 text-sky-800", thumb_bg: "bg-sky-50", icon_class: "fa-solid fa-align-left text-sky-400" }
  when 'investigation'
    { tag_class: "bg-purple-100 text-purple-800", thumb_bg: "bg-purple-50", icon_class: "fa-solid fa-flask text-purple-400" }
  when 'progress_check'
    { tag_class: "bg-amber-100 text-amber-800", thumb_bg: "bg-amber-50", icon_class: "fa-solid fa-check-double text-amber-400" }
  when 'rocket_thinking'
    { tag_class: "bg-pink-100 text-pink-800", thumb_bg: "bg-pink-50", icon_class: "fa-solid fa-rocket text-pink-400" }
  when 'video', 'song', 'expert', 'mission_assignment', 'timer'
    { tag_class: "bg-red-100 text-red-800", thumb_bg: "bg-red-50", icon_class: "fa-solid fa-video text-red-400" }
  when 'tour'
    { tag_class: "bg-indigo-100 text-indigo-800", thumb_bg: "bg-indigo-50", icon_class: "fa-solid fa-map-marked-alt text-indigo-400" }
  when 'phet'
    { tag_class: "bg-teal-100 text-teal-800", thumb_bg: "bg-teal-50", icon_class: "fa-solid fa-atom text-teal-400" }
  when 'quip_question'
    { tag_class: "bg-cyan-100 text-cyan-800", thumb_bg: "bg-cyan-50", icon_class: "fa-solid fa-comments text-cyan-400" } # Changed icon
  when 'complete_quiz'
    { tag_class: "bg-cyan-100 text-cyan-800", thumb_bg: "bg-cyan-50", icon_class: "fa-solid fa-comments text-cyan-400" } # Changed icon
  when 'quiz'
    { tag_class: "bg-yellow-100 text-yellow-800", thumb_bg: "bg-yellow-50", icon_class: "fa-solid fa-list-ol text-yellow-400" } # Changed icon
  when 'keywords', 'previous_keywords'
    { tag_class: "bg-lime-100 text-lime-800", thumb_bg: "bg-lime-50", icon_class: "fa-solid fa-tags text-lime-400" }
  when 'dynamic'
    { tag_class: "bg-slate-100 text-slate-800", thumb_bg: "bg-slate-50", icon_class: "fa-solid fa-puzzle-piece text-slate-400" }
  when 'homework'
    { tag_class: "bg-fuchsia-100 text-fuchsia-800", thumb_bg: "bg-fuchsia-50", icon_class: "fa-solid fa-file-word text-fuchsia-400" }
  else
    { tag_class: "bg-gray-100 text-gray-800", thumb_bg: "bg-gray-100", icon_class: "fa-regular fa-image text-gray-400" }
  end
end
%>

<!-- HTMX is loaded into this container where it is outside the form which will callback to replace 
a specific #preview-video -->
<div id="preview-video-loader" style="display: none;"></div>

<dialog id="slide-preview-dialog" class="cursor-default mx-auto w-full h-full px-8 py-4 bg-white border border-gray-200 rounded-lg shadow-lg dark:bg-gray-800 dark:border-gray-700">
  <div
    class="text-gray-500 hover:bg-slate-400 hover:text-white border-1 border-black w-10 h-10 cursor-pointer absolute top-0 right-0 bg-white rounded-full flex items-center justify-center shadow z-50"
    onclick="closeSlidePreview()"
  >
    <i class="fa-solid fa-xmark text-xl"></i>
  </div>
  <!-- Quizzes need a valid authenticity token,so wrapped with a form so it can be provided -->
  <%= form_with url: '', class: "h-full w-full" do %>
    <div class="flex h-full w-full" id="slide-preview-content"></div>
  <% end %>
</dialog>

<div
    id="slides-container"
    data-lesson-template-id="<%= @lesson_template.id %>"
  >
  <div class="flex justify-between items-center gap-2 p-4 border-b sticky top-0 bg-white rounded-t-lg z-10 flex-wrap">
    <div class="flex gap-4 items-center flex-wrap">
      <%- unless hide_ai %>
        <%= render ButtonComponent::Base.new(
            text: "Generate with AI",
            left_icon: "fa-solid fa-robot",
            onclick: "confirmAiGeneration()",
            variant: :purple
          ) %>
      <%- end %>
      <%= link_to static_presentation_path(@lesson_template, return_tab:"1"), target: :blank do %>
        <%= render ButtonComponent::Base.new(
            text: "View Presentation",
            left_icon: "fa-solid fa-external-link-alt",
            variant: :cyan,
          ) %>
      <% end %>
    </div>
    <%# New Slide Button (triggers drawer) %>
    <%= render(DrawerComponent.new(title: "Add New Slide", id: "new-slide-drawer", position: "right")) do |drawer| %>
      <% drawer.with_trigger do %>
        <button class="btn btn-base btn-cyan" id="add-slide-btn">
          <i class="fa-solid fa-plus mr-2"></i>Add Slide
        </button>
      <% end %>
      <%# New Slide Form %>
      <%- slide = Lesson::Slide.new %>
      <%= form_with(
            model: slide,
            url: new_slide_path.call(@lesson_template),
            local: true,
            scope: :slide,
            id: "new-slide-form"
          ) do |f| %>
        <div class="space-y-4">
          <div class="field-base">
            <%= f.label :slide_type, class: "block text-sm font-medium text-gray-700" %>
            <%= f.select :slide_type,
                  allowed_slide_types.map { |type| [Lesson::Slide.user_friendly_slide_type(type), type] },
                  { include_blank: "Select a slide type" },
                  { class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 slide-type-select", required: true }
                %>
            <%= render 'shared/slide_type_descriptions' %>
          </div>
          <%# Fields for all text-based slides: text, investigation, progress_check, rocket_thinking %>
          <div class="field-text field-investigation field-progress_check field-rocket_thinking field-video field-song field-expert field-mission_assignment field-quip_question field-complete_quiz field-tour field-phet field-timer field-keywords field-previous_keywords field-quiz field-homework">
            <%= f.label :body, "Top Text", class: "block text-sm font-medium text-gray-700" %>
            <%= f.text_area :body, rows: 4, class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2", placeholder: "Enter the main text for the slide..." %>
          </div>
          <div class="field-text field-investigation field-progress_check field-rocket_thinking field-video field-song field-expert field-mission_assignment field-quip_question field-complete_quiz field-tour field-phet field-timer field-keywords field-previous_keywords field-quiz field-homework">
            <%= f.label :footer, "Bottom Text", class: "block text-sm font-medium text-gray-700" %>
            <%= f.text_area :footer, rows: 2, class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2", placeholder: "Enter the footer text for the slide..." %>
          </div>
          <%# Image field for slides that support images %>
          <div class="field-text field-investigation field-progress_check field-rocket_thinking">
            <%= render "shared/form_fields/image_field",
                  form: f,
                  field_name: :fileboy_image_id,
                  current_image_id: slide.fileboy_image_id,
                  label: "Feature Image", 
                  preview_size: "300x_",
                  image_style: slide.image_style_type,
                  uniq_id: "new" 
              %>
          </div>
          <%# Image style type for slides with images %>
          <div class="field-image-style">
            <%= f.label :image_style_type, "Image Display Style", class: "block text-sm font-medium text-gray-700" %>
            <%= f.select :image_style_type,
                  Lesson::Slide.image_style_types.keys.map { |type| [type.titleize, type] },
                  {},
                  { class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2", onchange: "handleApplyImageDisplayStyle(event)" }
                %>
            <p class="text-xs text-gray-500 mt-1">Cover fills the space, Contain preserves aspect ratio</p>
          </div>
          <%# Homework Fields %>
          <div class="field-homework">
            <div name="homework-select" class="mb-4">
              <%= f.label :homework_id, 'Select from your independent learning' %>
              <%= f.select :homework_id, 
                options_for_select(@current_user.created_homeworks.map {|hw| [hw.title, hw.id]}, slide.homework_id),
                { include_blank: "Select from independent learning" },
                { class: "form-control", onchange: "loadHomeworkTasks(this)" }
              %>
            </div>
            <div name="task-select" class="mb-4 <%= slide.homework.present? ? '' : 'hidden' %>">
              <%= f.label :homework_task_id, 'Select a task from your homework (optional)' %>
              <%= f.select :homework_task_id, 
                options_for_select([], ''),
                { include_blank: "Select from tasks" },
                { class: "form-control" } 
              %>
            </div>
          </div>
          <%# Video fields %>
          <div class="field-video field-song field-expert field-mission_assignment">
            <%= render 'shared/form_fields/video_field', form: f, slide: slide, video: slide.video, limit_video_types: limit_video_types %>
          </div>
          <%# Timer Fields %>
          <div class="field-timer" data-field-timer>
            <%= f.label :timer_video_id, "Timer Video", class: "block text-sm font-medium text-gray-700" %>
            <%= f.select :timer_video_id, options_for_select(Lesson::Slide::TIMER_SLIDE_OPTIONS.map { |opt| [opt[:name], opt[:fileboy_id]]}, slide.video&.external_id) %>
            <div id="preview-video-container-new" data-uniq-id="new" class="mt-2"></div>
          </div>
          <%# Tour fields %>
          <div class="field-tour">
            <%= f.label :tour_id, "Tour", class: "block text-sm font-medium text-gray-700" %>
            <%= f.collection_select :tour_id, Tour.all, :id, :name,
                  { include_blank: "Select a tour" },
                  { class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2", onchange: "handleSynchronizeTourIdField(this)" } %>
          </div>
          <div class="field-tour" style="<%= slide.slide_type == 'tour' ? '' : 'display: none;' %>">
            <%= f.label :autoFocusVideoId, "Auto Focus on Video", class: "block text-sm font-medium text-gray-700" %>
            <%= f.select :autoFocusVideoId, 
              options_for_select((slide.tour&.tour_videos.presence || []).map {|v| [v[:full_name], v[:value]]}, slide.autoFocusVideoId),
              { include_blank: "Select from tour videos" },
              { class: "form-control" } 
            %>
          </div>
          <%# PHET interactive simulation fields %>
          <div class="field-phet">
            <%= f.label :iframe_src, "Iframe Source URL", class: "block text-sm font-medium text-gray-700" %>
            <%= f.text_field :iframe_src, class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2", placeholder: "https://phet.colorado.edu/sims/html/..." %>
            <p class="text-xs text-gray-500 mt-1">Full URL for the PHET simulation</p>
          </div>
          <%# Quip Question fields %>
          <%= render 'shared/form_fields/quiz_question_fields', form: f, slide: slide %>
          <%= render partial: 'static_admin/flows/quiz_field', locals: { form: f, quiz: slide.quiz, is_new: true, record_name: @lesson_template.name, field_type: 'complete_quiz', record_type: 'slide' } %>
          <div class="field-dynamic" style="display: none;">
            <%= render "shared/form_fields/dynamic_slide_fields", f: f, slide: slide %>
          </div>
          <%# Keywords fields - doesn't need additional fields %>
          <div class="field-keywords field-previous_keywords field-quiz">
            <div class="bg-yellow-50 p-4 rounded-md">
              <p class="text-sm text-yellow-800 text-wrap">
                <i class="fa-solid fa-info-circle mr-1"></i>
                No additional configuration needed for this slide type. It will automatically use data from elsewhere in the lesson.
              </p>
            </div>
          </div>
          <div class="flex justify-end space-x-2 pb-16">
            <button type="button" class="px-4 py-2 border rounded text-gray-700" data-action="drawer#close">
              Cancel
            </button>
            <%= f.submit "Save", class: "px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700" %>
          </div>
        </div>
      <% end %>
    <% end %>
  </div>
  <%# Slides Table %>
  <div id="slides-table">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">Order</th>
          <th class="py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
          <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thumbnail</th>
          <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Body</th>
          <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
      </thead>
      <tbody id="slides-table-body" class="bg-white divide-y divide-gray-200">
        <%# Hardcoded Intro Slide - Cannot be moved or edited %>
        <tr class="bg-blue-50 border-b-2 border-blue-200">
          <td class="px-4 py-4 whitespace-nowrap">
            <div class="flex items-center">
              <span class="slide-order font-medium">1</span>
              <i class="fa-solid fa-lock ml-2 text-gray-400"></i>
            </div>
          </td>
          <td class="py-4 whitespace-nowrap">
            <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
              Intro
            </span>
          </td>
          <td class="px-4 py-4 whitespace-nowrap">
            <div class="h-16 w-24 bg-blue-100 flex items-center justify-center rounded border border-blue-200">
              <i class="fa-solid fa-book-open text-blue-400 text-xl"></i>
            </div>
          </td>
          <td class="px-4 py-4">
            <div class="text-sm text-gray-900 max-w-md overflow-hidden line-clamp-2">
              Introduction to <%= @lesson_template.name %>
            </div>
          </td>
          <td class="px-4 py-4 whitespace-nowrap">
            <div class="flex items-center">
              <span class="text-sm text-gray-500 italic">System slide (cannot be edited)</span>
            </div>
          </td>
        </tr>
        <%# Regular Slides - Can be moved and edited %>
        <% @slides.includes([homework: :tasks]).each_with_index do |slide, index| %>
          <% styles = slide_type_styles(slide.slide_type) %>
          <tr class="regular-slide" data-slide-id="<%= slide.id %>">
            <td class="px-4 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <span class="slide-order font-medium"><%= index + 2 %></span>
                <i class="fa-solid fa-grip-vertical ml-2 text-gray-400 handle"></i>
              </div>
            </td>
            <td class="py-4 whitespace-nowrap w-1">
              <span class="px-2 py-1 text-xs font-medium rounded-full <%= styles[:tag_class] %>">
                <%= slide.user_friendly_slide_type %>
              </span>
            </td>
            <td class="px-4 py-4 whitespace-nowrap w-36">
              <% if slide.thumbnail.present? %>
                <img src="<%= slide.thumbnail %>" alt="Slide thumbnail" class="h-16 w-24 <%= slide.image_style_type == 'contain' ? 'object-contain' : 'object-cover' %> rounded border border-gray-200">
              <% else %>
                <div class="h-16 w-24 <%= styles[:thumb_bg] %> flex items-center justify-center rounded border <%= styles[:thumb_bg].gsub('50', '200').gsub('bg-', 'border-') %>">
                  <i class="<%= styles[:icon_class] %> text-xl"></i>
                </div>
              <% end %>
            </td>
            <td class="px-4 py-4">
              <div class="text-sm text-gray-900 max-w-2xl overflow-hidden line-clamp-2">
                <%= slide.slide_type == 'dynamic' ? (slide.data&.[]('top_text') || 'Dynamic Slide') : slide.body.to_s.truncate(250) %>
              </div>
              <div class="text-sm text-gray-900 max-w-2xl overflow-hidden line-clamp-2 mt-1">
                <%= slide.slide_type == 'dynamic' ? (slide.data&.[]('bottom_text') || 'Dynamic Slide') : slide.footer.to_s.truncate(250) %>
              </div>
              <% if slide.slide_type == 'dynamic' %>
                <div class="text-xs text-gray-500 mt-1 truncate">
                  <% panel_count = slide.data&.[]('panels')&.length || 0 %>
                  <%= panel_count %> <%= 'panel'.pluralize(panel_count) %>
                  <div>
                    <% slide.data&.[]('panels')&.map&.with_index do |panel, index| %>
                      <p>
                        <span><b><%= index+1%></b>: <%= panel['media_type'].titleize %> - </span>
                        <% if panel['media_type'] == 'image' && panel['label'].present? %>
                          <span class="text-wrap"><%= panel['label'] %> </span>
                        <% end %>
                        <span>
                          <%=
                            if panel['media_type'] == 'text'
                              panel['text']&.truncate(120)
                            elsif panel['media_type'] == 'video'
                              raw_slide = slide.data_for_form rescue {}
                              "#{raw_slide['panels'][index]['video_source'].titleize} (#{raw_slide['panels'][index]['external_video_id']})"
                            elsif panel['media_type'] == 'image'
                              panel['image_url']
                            else
                              ''
                            end
                          %>
                        </span>
                      </p>
                    <% end %>
                  </div>
                </div>
              <% end %>
              <% if slide.template.present? && slide.slide_type == 'keywords' %>
                <% words = slide.template.keywords.map do |keyword| keyword.name end %>
                <div class="text-sm text-gray-900 max-w-2xl overflow-hidden line-clamp-2 mt-1">
                  <%= words.join(", ") %>
                </div>
              <% end %>
              <% if slide.template.present? && slide.slide_type == 'quiz' %>
                <% questions = slide.template.keywords.includes([:quiz_question]).map do |keyword| keyword.quiz_question&.body end.compact %>
                <% questions.each do |question| %>
                  <div class="text-sm text-gray-900 max-w-2xl overflow-hidden line-clamp-2 mt-1">
                    <%= question %>
                  </p>
                <% end %>
              <% end %>
              <% if slide.video_type? %>
                <% if slide.video && slide.video.external_id.present? %>
                  <div class="text-xs text-gray-500 mt-1 truncate">
                    <% if slide.slide_type == 'timer' %>
                      <p><%= Lesson::Slide::TIMER_SLIDE_OPTIONS.find { |opt| opt[:fileboy_id] == slide.video.external_id }&.dig(:name) %></p>
                    <% end %>
                    <i class="fa-solid fa-link mr-1"></i><%= slide.video.source.titleize %>: <%= slide.video.external_id %>
                  </div>
                <% elsif slide.fileboy_video_id.present? %>
                  <div class="text-xs text-gray-500 mt-1 truncate">
                    <i class="fa-solid fa-link mr-1"></i>Fileboy: <%= slide.fileboy_video_id %>
                  </div>
                <% elsif slide.video_url.present? %>
                  <div class="text-xs text-gray-500 mt-1 truncate">
                    <i class="fa-solid fa-link mr-1"></i><%= slide.video_url %>
                  </div>
                <% end %>
                <% if slide.loop_video %>
                  <div class="text-xs text-gray-500 mt-1 truncate">
                    <i class="fa-regular fa-circle-video mr-1"></i> Video will loop
                  </div>
                <% end %>
              <%  end %>
              <% if slide.slide_type == 'phet' && slide.iframe_src.present? %>
                <div class="text-xs text-gray-500 mt-1 truncate">
                  <i class="fa-solid fa-link mr-1"></i><%= slide.iframe_src %>
                </div>
              <% end %>
              <% if slide.slide_type == 'tour' && slide.tour.present? %>
                <div class="text-xs text-gray-500 mt-1 truncate">
                  <%= slide.tour.name %>
                </div>
                <% if slide.autoFocusVideoId.present? %>
                  <% scene = slide.tour.scene_by_video_id(slide.autoFocusVideoId) %>
                  <% if scene.present? %>
                    <div class="text-xs text-gray-500 mt-1 truncate">
                      <i class="fa-solid fa-eye mr-1"></i><%= scene['name'] %>
                    </div>
                  <% end %>
                <% end %>
              <% end %>
              <% if slide.slide_type == 'quip_question' && slide.quip_question.present? %>
                <div class="text-xs text-gray-500 mt-1 truncate text-wrap">
                  <%= slide.quip_question.question_type.humanize.titleize %>: <%= slide.quip_question.data_json['prompt']&.truncate(250) %>
                </div>
              <% end %>
              <% if slide.slide_type == 'complete_quiz' && slide.quiz.present? %>
                <div class="text-xs text-gray-500 mt-1 truncate text-wrap">
                    <%= slide.quiz.name %>: <%= pluralize(slide.quiz.quip_questions_count, 'question') %>
                </div>
              <% end %>
              <% if slide.slide_type == 'homework' && slide.homework.present? %>
                <div class="text-xs text-gray-500 mt-1 truncate">
                  <h4><%= slide.homework.title %></h4>
                  <% if slide.homework.date_due.present? %>
                    <p>Due: <%= slide.homework.date_due.strftime("%d/%m/%Y") %></p>
                  <% end %>
                  <p>Assigned to: <%= slide.homework.pupils.count %> <%= "pupil".pluralize(slide.homework.pupils.count) %></p>
                  <% if slide.homework_task.present? %>
                    <p>For task: <%= slide.homework_task.task_type.humanize.titleize %> - <%= slide.homework_task.title %></p>
                  <% else %>
                    <p><%= slide.homework.tasks.count %> <%= "task".pluralize(slide.homework.tasks.count) %></p>
                  <% end %>
                </div>
              <% end %>
            </td>
            <td class="px-4 py-4 whitespace-nowrap">
              <div class="flex items-center space-x-3">
                <% if @current_user.beta_feature_enabled?(:aug_4) %>
                  <%# Preview Button %>
                  <button onclick="handleSlidePreview(this, '<%= slide.id %>')" class="inline-flex items-center px-2.5 py-1.5 border border-blue-300 text-sm font-medium rounded text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="fa-solid fa-eye mr-1"></i>Preview
                  </button>
                <% end %>
                <%# Edit Slide Drawer %>
                <%= render(DrawerComponent.new(title: "Edit Slide", id: "edit-slide-#{slide.id}-drawer", position: "right")) do |drawer| %>
                  <% drawer.with_trigger do %>
                    <button data-edit-button class="inline-flex items-center px-2.5 py-1.5 border border-blue-300 text-sm font-medium rounded text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                      <i class="fa-solid fa-pen-to-square mr-1"></i>Edit
                    </button>
                  <% end %>
                  <%# Edit Slide Form %>
                  <%= form_with(
                        model: slide,
                        url: edit_slide_path.call(@lesson_template, slide),
                        method: :patch,
                        local: true,
                        scope: :slide,
                        id: "edit-slide-form-#{slide.id}"
                      ) do |f| %>
                    <div class="space-y-4">
                      <div class="field-base">
                        <%= f.label :slide_type, class: "block text-sm font-medium text-gray-700" %>
                        <%# Adds the current slide type onto the array incase it was removed by the filtered 
                            so that slides that are no longer creatable still work (e.g from duplicated templates) %>
                        <%= f.select :slide_type,
                              (allowed_slide_types + [slide.slide_type]).uniq.map { |type| [Lesson::Slide.user_friendly_slide_type(type), type] },
                              {},
                              { class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 slide-type-select", required: true }
                            %>
                        <%= render 'shared/slide_type_descriptions' %>
                      </div>
                      <%# Fields for all text-based slides: text, investigation, progress_check, rocket_thinking %>
                      <div class="field-text field-investigation field-progress_check field-rocket_thinking field-video field-song field-expert field-mission_assignment field-quip_question field-complete_quiz field-tour field-phet field-timer field-keywords field-previous_keywords field-quiz field-homework">
                        <%= f.label :body, "Top Text", class: "block text-sm font-medium text-gray-700" %>
                        <%= f.text_area :body, rows: 4, class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2", placeholder: "Enter the main text for the slide..." %>
                      </div>
                      <div class="field-text field-investigation field-progress_check field-rocket_thinking field-video field-song field-expert field-mission_assignment field-quip_question field-complete_quiz field-tour field-phet field-timer field-keywords field-previous_keywords field-quiz field-homework">
                        <%= f.label :footer, "Bottom Text", class: "block text-sm font-medium text-gray-700" %>
                        <%= f.text_area :footer, rows: 2, class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2", placeholder: "Enter the footer text for the slide..." %>
                      </div>
                      <%# Image field for slides that support images %>
                      <div class="field-text field-investigation field-progress_check field-rocket_thinking">
                        <%= render "shared/form_fields/image_field",
                              form: f,
                              field_name: :fileboy_image_id,
                              current_image_id: slide.fileboy_image_id,
                              label: "Feature Image", 
                              preview_size: "300x_",
                              image_style: slide.image_style_type,
                              uniq_id: slide.id.to_s 
                          %>
                      </div>
                      <%# Image style type for slides with images %>
                      <div class="field-image-style">
                        <%= f.label :image_style_type, "Image Display Style", class: "block text-sm font-medium text-gray-700" %>
                        <%= f.select :image_style_type,
                              Lesson::Slide.image_style_types.keys.map { |type| [type.titleize, type] },
                              {},
                              { class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2", onchange: "handleApplyImageDisplayStyle(event)" }
                            %>
                        <p class="text-xs text-gray-500 mt-1">Cover fills the space, Contain preserves aspect ratio</p>
                      </div>
                      <%# Homework Fields %>
                      <div class="field-homework">
                        <div name="homework-select" class="mb-4">
                          <%= f.label :homework_id, 'Select from your independent learning' %>
                          <%= f.select :homework_id, 
                            options_for_select(@current_user.created_homeworks.map {|hw| [hw.title, hw.id]}, slide.homework_id),
                            { include_blank: "Select from independent learning" },
                            { class: "form-control", onchange: "loadHomeworkTasks(this)" } 
                          %>
                        </div>
                        <div name="task-select" class="mb-4 <%= slide.homework.present? ? '' : 'hidden' %>">
                          <%= f.label :homework_task_id, 'Select a task from your homework (optional)' %>
                          <%= f.select :homework_task_id, 
                            options_for_select(slide.homework&.tasks&.map {|t| [t.title, t.id]} || [], slide.homework_task_id),
                            { include_blank: "Select from tasks" },
                            { class: "form-control" } 
                          %>
                        </div>
                      </div>
                      <%# Video fields %>
                      <div class="field-video field-song field-expert field-mission_assignment">
                        <%= render 'shared/form_fields/video_field', form: f, slide: slide, video: slide.video, limit_video_types: limit_video_types %>
                      </div>
                      <%# Timer Fields %>
                      <div class="field-timer" data-field-timer>
                        <%= f.label :timer_video_id, "Timer Video", class: "block text-sm font-medium text-gray-700" %>
                        <%= f.select :timer_video_id, options_for_select(Lesson::Slide::TIMER_SLIDE_OPTIONS.map { |opt| [opt[:name], opt[:fileboy_id]]}, slide.video&.external_id) %>
                        <div id="preview-video-container-<%=slide.id%>" data-uniq-id="<%=slide.id%>" class="mt-2"></div>
                      </div>
                      <%# Tour fields %>
                      <div class="field-tour" style="<%= slide.slide_type == 'tour' ? '' : 'display: none;' %>">
                        <%= f.label :tour_id, "Tour", class: "block text-sm font-medium text-gray-700" %>
                        <%= f.collection_select :tour_id, Tour.all, :id, :name,
                          { include_blank: "Select a tour" }, # Retain for optional field
                          { class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2", onchange: "handleSynchronizeTourIdField(this)" } %>
                      </div>
                      <div class="field-tour" style="<%= slide.slide_type == 'tour' ? '' : 'display: none;' %>">
                        <%= f.label :autoFocusVideoId, "Auto Focus on Video", class: "block text-sm font-medium text-gray-700" %>
                        <%= f.select :autoFocusVideoId, 
                          options_for_select((slide.tour&.tour_videos.presence || []).map {|v| [v[:full_name], v[:value]]}, slide.autoFocusVideoId),
                          { include_blank: "Select from tour videos" },
                          { class: "form-control" } 
                        %>
                      </div>
                      <%# PHET interactive simulation fields %>
                      <div class="field-phet" style="<%= slide.slide_type == 'phet' ? '' : 'display: none;' %>">
                        <%= f.label :iframe_src, "Iframe Source URL", class: "block text-sm font-medium text-gray-700" %>
                        <%= f.text_field :iframe_src, class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2", placeholder: "https://phet.colorado.edu/sims/html/..." %>
                        <p class="text-xs text-gray-500 mt-1">Full URL for the PHET simulation</p>
                      </div>
                      <%# Quip Question fields %>
                      <%= render 'shared/form_fields/quiz_question_fields', form: f, slide: slide %>
                      <%= render partial: 'static_admin/flows/quiz_field', locals: { form: f, quiz: slide.quiz, is_new: true, record_name: @lesson_template.name, field_type: 'complete_quiz', record_type: 'slide' } %>
                      <div class="field-dynamic" style="<%= slide.slide_type == 'dynamic' ? '' : 'display: none;' %>">
                        <%= render "shared/form_fields/dynamic_slide_fields", f: f, slide: slide %>
                      </div>
                      <%# Keywords fields - doesn't need additional fields %>
                      <div class="field-keywords field-previous_keywords field-quiz">
                        <div class="bg-yellow-50 p-4 rounded-md">
                          <div class="text-sm text-yellow-800 flex gap-2 items-center">
                            <p class="text-sm text-yellow-800 text-wrap">
                              <i class="fa-solid fa-info-circle mr-1"></i>
                              No additional configuration needed for this slide type. It will automatically use data from elsewhere in the lesson.
                            </p>
                          </div>
                        </div>
                      </div>
                      <div class="flex justify-end space-x-2 pb-16">
                        <button type="button" class="px-4 py-2 border rounded text-gray-700" data-action="drawer#close">
                          Cancel
                        </button>
                        <%= f.submit "Update", class: "px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700" %>
                      </div>
                    </div>
                  <% end %>
                <% end %>
                <%# Delete Button %>
                <%= button_to delete_slide_path.call(@lesson_template, slide),
                        method: :delete,
                        data: { confirm: "Are you sure you want to delete this slide?" },
                        class: "inline-flex items-center px-2.5 py-1.5 border border-red-300 text-sm font-medium rounded text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500" do %>
                  <i class="fa-solid fa-trash-alt mr-1"></i>Delete
                <% end %>
              </div>
            </td>
          </tr>
        <% end %>
        <%# Hardcoded Outro Slide - Cannot be moved or edited %>
        <tr class="bg-green-50 border-t-2 border-green-200">
          <td class="px-4 py-4 whitespace-nowrap">
            <div class="flex items-center">
              <span class="slide-order font-medium"><%= @slides.count + 2 %></span>
              <i class="fa-solid fa-lock ml-2 text-gray-400"></i>
            </div>
          </td>
          <td class="py-4 whitespace-nowrap">
            <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
              Outro
            </span>
          </td>
          <td class="px-4 py-4 whitespace-nowrap">
            <div class="h-16 w-24 bg-green-100 flex items-center justify-center rounded border border-green-200">
              <i class="fa-solid fa-flag-checkered text-green-400 text-xl"></i>
            </div>
          </td>
          <td class="px-4 py-4">
            <div class="text-sm text-gray-900 max-w-md overflow-hidden line-clamp-2">
              Conclusion for <%= @lesson_template.name %>
            </div>
          </td>
          <td class="px-4 py-4 whitespace-nowrap">
            <div class="flex items-center">
              <span class="text-sm text-gray-500 italic">System slide (cannot be edited)</span>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
<style>
  /* Styles for the dragging state */
  tr.dragging {
    background-color: #edf5ff !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    opacity: 0.9;
  }

  /* Hide browser's default drag ghost image */
  [data-sortable-ghost] {
    opacity: 0;
  }

  /* Highlight the row when sorting */
  tr.sortable-chosen {
    background-color: #f0f9ff !important;
  }

  /* Make the handle more visually clickable */
  .handle {
    cursor: grab;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.2s;
  }

  .handle:hover {
    background-color: #e5e7eb;
  }

  .handle:active {
    cursor: grabbing;
  }

  #slides-table {
    overflow-x: auto;
  }

  /* Prevent default browser drag behavior on table rows */
  #slides-table-body tr {
    -webkit-user-drag: none;
    user-drag: none;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }

  /* Additional fixes for Chrome's globe icon */
  .sortable-drag {
    opacity: 0 !important;
  }

  /* Line clamp for truncating body text */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
<script>
  // Cache scroll position before any form submit
  document.addEventListener('DOMContentLoaded', function() {
    console.log("binding scroll position to forms");
    document.querySelectorAll('form').forEach(form => {
      console.log("binding scroll position to forms", form);
      form.addEventListener('submit', () => {
        sessionStorage.setItem('scrollY', window.scrollY);
      });
    });

    // Restore scroll position on page load, then clear it
    const scrollY = sessionStorage.getItem('scrollY');
    if (scrollY !== null) {
      window.scrollTo(0, parseInt(scrollY, 10));
      sessionStorage.removeItem('scrollY');
    }
  });
</script>
<script>
  document.addEventListener('drawer:opened', (event) => {
    const node = event.detail.target;
    
    // Trigger quiz link loading
    const questionIdField = node.querySelector('[name="slide[quiz_id]"]');
    handleLoadQuipQuizLink(questionIdField);
  })
  // =============================================================================
  // SLIDE MANAGEMENT JAVASCRIPT
  // =============================================================================
  // Handles slide editing, reordering, video previews, and form interactions
  // for the lesson template management interface.

  // =============================================================================
  // MARK: - UTILITIES
  // =============================================================================

  /**
   * Traverses up the DOM tree to find a child element with the specified selector
   * @param {HTMLElement} node - Starting node to traverse from
   * @param {string} selector - CSS selector to search for
   * @returns {HTMLElement|null} - Found element or null if not found
   */
  function findChildNodeInParentTree(node, selector) {
    let parent = node;
    let depth = 0;
    let selected = null;
    
    while (!selected && depth < 4) {
      parent = parent.parentElement;
      selected = parent.querySelector(selector);
      depth++;
    }
    
    if (!selected) {
      console.warn("Could not find a parent element with selector: " + selector);
    }
    
    return selected;
  }

  /**
   * Adds a parameter to a form's action URL
   * @param {HTMLFormElement} form - Form element to modify
   * @param {string} name - Parameter name
   * @param {string} value - Parameter value
   */
  function addParamToFormAction(form, name, value) {
    const currentAction = form.action;
    const url = new URL(currentAction);
    url.searchParams.set(name, value);
    form.action = url.toString();
  }

  /**
   * Shows or hides the quiz generation panel
   * @param {HTMLElement} fromNode - Node to search from
   * @param {boolean} visible - Whether to show the panel
   * @returns {HTMLElement} - The panel element
   */
  function setGenerateQuizPanelVisible(fromNode, visible) {
    const generatePanelNode = findChildNodeInParentTree(fromNode, '#generate-quiz-panel');
    generatePanelNode.style.display = visible ? 'block' : 'none';
    return generatePanelNode;
  }

  /**
   * Shows or hides the quiz link node
   * @param {HTMLElement} fromNode - Node to search from
   * @param {boolean} visible - Whether to show the link
   * @returns {HTMLElement} - The link element
   */
  function setQuizLinkNodeVisible(fromNode, visible) {
    const quizLinkNode = findChildNodeInParentTree(fromNode, '#quip-question-id-link');
    quizLinkNode.style.display = visible ? 'block' : 'none';
    return quizLinkNode;
  }

  // =============================================================================
  // MARK: - IMAGE DISPLAY HANDLING
  // =============================================================================

  /**
   * Handles changing image display styles (cover/contain)
   * @param {Event} event - Change event from select element
   */
  function handleApplyImageDisplayStyle(event) {
    const option = event.target.selectedOptions[0];
    const imageElement = event.target.parentElement.previousElementSibling.querySelector('img');
    
    if (!imageElement) {
      return;
    }
    
    if (option.value == 'cover') {
      imageElement.classList.add('object-cover');
      imageElement.classList.remove('object-contain');
    }
    
    if (option.value == 'contain') {
      imageElement.classList.add('object-contain');
      imageElement.classList.remove('object-cover');
    }
  }

  // =============================================================================
  // MARK: - SLIDE TYPE FORM MANAGEMENT
  // =============================================================================

  /**
   * Initializes slide type form handlers
   */
  function initSlideTypeForms() {
    const slideTypeSelects = document.querySelectorAll('.slide-type-select');

    slideTypeSelects.forEach(select => {
      handleSlideTypeChange(select);
      select.addEventListener('change', function() {
        handleSlideTypeChange(this);
      });
    });
  }

  /**
   * Handles slide type changes and shows/hides appropriate form fields
   * @param {HTMLSelectElement} selectElement - The slide type select element
   */
  function handleSlideTypeChange(selectElement) {
    const slideType = selectElement.value;
    const formContainer = selectElement.closest('form');

    if (!formContainer) return;

    // Hide all field groups initially
    const allFieldGroups = formContainer.querySelectorAll('[class*="field-"]');
    allFieldGroups.forEach(group => {
      if (group.classList.contains('field-base')) return;
      
      let isTypeSpecificField = false;
      group.classList.forEach(cls => {
        if (cls.startsWith('field-') && cls !== 'field-base' && cls !== 'field-image-style' && cls !== 'field-dynamic') {
          isTypeSpecificField = true;
        }
      });

      if (isTypeSpecificField) {
        group.style.display = 'none';
      }
    });

    // Hide all potentially relevant groups first
    formContainer.querySelectorAll('.field-text, .field-investigation, .field-progress_check, .field-rocket_thinking, .field-video, .field-song, .field-expert, .field-mission_assignment, .field-quip_question, .field-complete_quiz, .field-tour, .field-phet, .field-image-style, .field-dynamic, .field-keywords, .field-previous_keywords, .field-quiz').forEach(group => {
      if (!group.classList.contains('field-base')) {
        group.style.display = 'none';
      }
    });

    // Show fields for the selected slide type
    const fieldsToShow = formContainer.querySelectorAll(`.field-${slideType}`);
    fieldsToShow.forEach(field => field.style.display = 'block');

    // Special handling for image style (only if image fields are relevant for the type)
    const imageStyleField = formContainer.querySelector('.field-image-style');
    if (imageStyleField) {
      const showImageStyle = ['text', 'investigation', 'progress_check', 'rocket_thinking'].includes(slideType);
      imageStyleField.style.display = showImageStyle ? 'block' : 'none';
    }

    // Special handling for the common text/body field shared by many types
    if (['text', 'investigation', 'progress_check', 'rocket_thinking', 'video', 'song', 'expert', 'mission_assignment', 'quip_question', 'tour', 'phet'].includes(slideType)) {
      const commonBodyField = formContainer.querySelector('.field-text.field-investigation.field-progress_check.field-rocket_thinking.field-video.field-song.field-expert.field-mission_assignment.field-quip_question.field-complete_quiz.field-tour.field-phet');
      if (commonBodyField) commonBodyField.style.display = 'block';
    }

    // Special handling for image fields shared by some types
    if (['text', 'investigation', 'progress_check', 'rocket_thinking'].includes(slideType)) {
      const commonImageField = formContainer.querySelector('.field-text.field-investigation.field-progress_check.field-rocket_thinking');
      if (commonImageField && !commonImageField.isEqualNode(formContainer.querySelector('.field-image-style'))) {
        commonImageField.style.display = 'block';
      }
    }
  }

  /**
   * Sets slide type description visibility and content
   * @param {HTMLSelectElement} node - The slide type select element
   */
  function setSlideTypeDescription(node) {
    const selected = node.selectedOptions[0]?.value;
    const container = findChildNodeInParentTree(node, `[name='slide-type-description']`);
    
    if (!selected) {
      container.classList.add("hidden");
    } else {
      container.classList.remove("hidden");
    }
    
    const query = `[name='slide-type-desc-${selected}']`;
    const desc = container.querySelector(query);
    if (!desc) {
      return;
    }
    
    const allEls = node.parentElement.querySelectorAll(`[name^='slide-type-desc-']`);
    allEls.forEach(n => {
      if (!n.classList.contains('hidden')) {
        n.classList.add('hidden');
      }
    });
    desc.classList.remove('hidden');
  }

  /**
   * Toggles visibility of legacy video fields based on their content
   * @param {HTMLElement} node - The slide type select element
   */
  function toggleLegacyVideoFields(node) {
    const container = findChildNodeInParentTree(node, `[name='legacy-video-container']`);
    if (!container) {
      return;
    }
    
    const fbvId = container.querySelector(`[name='slide[fileboy_video_id]']`)?.value;
    const videoUrl = container.querySelector(`[name='slide[video_url]']`)?.value;
    
    if (!fbvId && !videoUrl) {
      container.style.display = 'none';
    } else {
      container.style.display = 'block';
    }
  }

  // =============================================================================
  // MARK: - SORTABLE FUNCTIONALITY
  // =============================================================================

  /**
   * Initializes the sortable functionality for slides table
   */
  function initSortable() {
    const slidesTableBody = document.getElementById('slides-table-body');
    const slidesContainer = document.getElementById('slides-container');

    if (!slidesTableBody || !slidesContainer) {
      return;
    }

    if (typeof Sortable === 'undefined') {
      console.error('Sortable.js is not loaded');
      return;
    }

    // Remove any existing sortable instance to avoid duplicates
    if (slidesTableBody.sortableInstance) {
      slidesTableBody.sortableInstance.destroy();
    }

    // Initialize Sortable with improved drag appearance and filtering out the hardcoded slides
    slidesTableBody.sortableInstance = Sortable.create(slidesTableBody, {
      animation: 150,
      handle: '.handle',
      ghostClass: 'bg-blue-100',
      dragClass: 'sortable-drag',
      chosenClass: 'sortable-chosen',
      forceFallback: true,
      fallbackClass: 'sortable-fallback',
      filter: '.bg-blue-50, .bg-green-50',

      onMove: function(evt) {
        const introSlide = slidesTableBody.querySelector('.bg-blue-50');
        const outroSlide = slidesTableBody.querySelector('.bg-green-50');

        if (evt.related === introSlide || evt.related === outroSlide) {
          return false;
        }

        return true;
      },

      onChoose: function(evt) {
        evt.item.classList.add('dragging');
      },

      onUnchoose: function(evt) {
        evt.item.classList.remove('dragging');
      },

      onEnd: function(evt) {
        // Skip if we're trying to sort a filtered item
        if (evt.item.classList.contains('bg-blue-50') || evt.item.classList.contains('bg-green-50')) {
          return;
        }

        // Get all slide IDs in the new order (excluding hardcoded slides)
        const slideIds = Array.from(slidesTableBody.querySelectorAll('tr[data-slide-id]'))
          .map(el => el.getAttribute('data-slide-id'));

        // Update the visual order numbers, accounting for the hardcoded intro slide
        Array.from(slidesTableBody.querySelectorAll('tr:not(.bg-blue-50):not(.bg-green-50) .slide-order')).forEach((el, index) => {
          el.textContent = index + 2;
        });

        // Get the lesson template ID
        const lessonTemplateId = slidesContainer.getAttribute('data-lesson-template-id');

        // Send the new order to the server
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;
        if (!csrfToken) {
          console.error('CSRF token not found');
          return;
        }

        const route = "<%= reorder_slides_path.call(@lesson_template) %>";
        fetch(route, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': csrfToken,
            'Accept': 'application/json'
          },
          body: JSON.stringify({ slide_ids: slideIds })
        })
        .then(response => {
          if (!response.ok) {
            response.json().then(data => console.error('Failed to update slide order:', data.error || 'Unknown error'));
          }
        })
        .catch(error => {
          console.error('Error updating slide order:', error);
        });
      }
    });
  }

  /**
   * Prevents default drag behavior for table rows
   */
  function preventDefaultDrag() {
    const rows = document.querySelectorAll('#slides-table-body tr');
    rows.forEach(row => {
      row.addEventListener('dragstart', function(e) {
        if (!e.target.closest('.handle')) {
          e.preventDefault();
        }
      });

      row.setAttribute('draggable', 'false');
    });
  }

  // =============================================================================
  // MARK: - VIDEO FIELD MANAGEMENT
  // =============================================================================

  /**
   * Builds a div that will trigger the preview video to load
   * @param {Object} args - Video configuration arguments
   * @param {string} uniqId - Unique identifier for the video container
   * @returns {HTMLElement} - The video loader element
   */
  function buildPreviewTemplate(args, uniqId) {
    const containerId = "#preview-video-container-" + uniqId;
    const previewContainer = document.querySelector(containerId);
    console.log("CONTAINER", uniqId, containerId, previewContainer);
    previewContainer.innerHTML = '';
    
    const div = document.createElement('div');
    div.id = 'preview-video';
    div.setAttribute("hx-headers", '{"X-Csrf-Token": "<%= form_authenticity_token %>"}');
    div.setAttribute('hx-post', "<%= videos_from_data_path %>");
    div.setAttribute('hx-vals', JSON.stringify(args));
    div.setAttribute('hx-trigger', 'load_preview');
    div.setAttribute('hx-target', containerId);
    div.setAttribute('hx-swap', 'innerHTML');
    div.setAttribute('form', 'none');
    
    if (args.fileboy_video_id || args.source == 'fileboy') {
      div.setAttribute('hx-on', 'htmx:afterRequest: if (event.target === this) { fb.reloadVideoPlayers() }');
    }
    
    return div;
  }

  /**
   * Handles video field preview creation and updates
   * @param {HTMLElement} node - Field within the video fields panel
   */
  function handleVideoFieldPreview(node) {
    const uniqId = findChildNodeInParentTree(node, '[data-uniq-id]').getAttribute('data-uniq-id');
    const loader = document.querySelector('#preview-video-loader');
    
    const videoSource = findChildNodeInParentTree(node, '[name="slide[video_source]"]')?.value;
    const externalVideoId = findChildNodeInParentTree(node, '[name="slide[external_video_id]"]')?.value;
    const fileboyVideoId = findChildNodeInParentTree(node, '[name="slide[fileboy_video_id]"]')?.value;
    const videoUrl = findChildNodeInParentTree(node, '[name="slide[video_url]"]')?.value;

    const videoLoader = buildPreviewTemplate({ 
      source: videoSource, 
      external_id: externalVideoId, 
      video_url: videoUrl, 
      fileboy_video_id: fileboyVideoId 
    }, uniqId);

    if (videoLoader && loader) {
      loader.innerHTML = '';
      loader.appendChild(videoLoader);
      htmx.process(videoLoader);
      htmx.trigger(videoLoader, 'load_preview');
    }
  }

  /**
   * Handles selection of existing videos from dropdown
   * @param {HTMLSelectElement} node - The video select element
   */
  function handleExistingVideoSelect(node) {
    const option = node.options[node.selectedIndex];
    const textValue = option.text;
    const matches = /(.+?)\s*\((\w+):\s*([^)]+)\)/.exec(textValue);

    if (matches.length == 4) {
      const [_, title, v_source, v_id] = matches;
      const videoSource = findChildNodeInParentTree(node, '[name="slide[video_source]"]');
      const externalVideoId = findChildNodeInParentTree(node, '[name="slide[external_video_id]"]');

      if (!videoSource || !externalVideoId) {
        console.error("Could not find video source or external video ID field");
        return;
      }

      const source = v_source.toLowerCase();
      const externalId = v_id;

      videoSource.value = source;
      videoSource.dispatchEvent(new Event('change', { bubbles: true }));

      externalVideoId.value = externalId;
      externalVideoId.dispatchEvent(new Event('change', { bubbles: true }));

      handleVideoFieldPreview(externalVideoId);
    }
  }

  /**
   * Converts legacy video fields to the new format
   * @param {HTMLElement} node - The conversion trigger element
   */
  async function handleConvertLegacyVideoFields(node) {
    const fileboyVideoId = findChildNodeInParentTree(node, '[name="slide[fileboy_video_id]"]');
    const videoUrl = findChildNodeInParentTree(node, '[name="slide[video_url]"]');
    const videoSource = findChildNodeInParentTree(node, '[name="slide[video_source]"]');
    const externalVideoId = findChildNodeInParentTree(node, '[name="slide[external_video_id]"]');

    // If we have a fileboy video ID, we can just set the source and external ID
    if (fileboyVideoId?.value) {
      videoSource.value = 'fileboy';
      videoSource.dispatchEvent(new Event('change', { bubbles: true }));
      externalVideoId.value = fileboyVideoId?.value;
      externalVideoId.dispatchEvent(new Event('change', { bubbles: true }));
      fileboyVideoId.value = '';
      fileboyVideoId.dispatchEvent(new Event('change', { bubbles: true }));
      handleVideoFieldPreview(externalVideoId);
      return;
    }

    if (!videoUrl?.value) {
      console.error("No video URL found");
      return;
    }

    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;
    if (!csrfToken) {
      console.error('CSRF token not found');
      return;
    }
    
    const response = await fetch("<%= convert_video_url_path %>", {
      method: "POST",
      body: JSON.stringify({ video_url: videoUrl?.value }),
      headers: { "content-type": "application/json", 'X-CSRF-Token': csrfToken }
    });

    if (!response.ok) {
      const error = await response.json();
      console.error("Error converting video URL", error);
      return null;
    }

    const data = await response.json();

    if (!data.source || !data.external_id) {
      console.error("No source or external ID found in response");
      return;
    }

    videoSource.value = data.source;
    videoSource.dispatchEvent(new Event('change', { bubbles: true }));
    externalVideoId.value = data.external_id;
    externalVideoId.dispatchEvent(new Event('change', { bubbles: true }));
    videoUrl.value = '';
    videoUrl.dispatchEvent(new Event('change', { bubbles: true }));
    handleVideoFieldPreview(externalVideoId);
  }

  /**
   * Updates placeholder text for external video ID field based on source
   * @param {HTMLSelectElement} node - The video source select element
   */
  function handleSourceToPlaceholder(node) {
    const value = node.value;
    let placeholder = '';
    
    if (value == 'youtube') {
      placeholder = 'e.g 4tVSguU_T6A';
    }
    if (value == 'vimeo') {
      placeholder = 'e.g 123456789';
    }
    if (value == 'fileboy') {
      placeholder = 'e.g 123e4567-e89b-12d3-a456-************';
    }
    
    findChildNodeInParentTree(node, '[name="slide[external_video_id]"]')?.setAttribute('placeholder', placeholder);
  }

  /**
   * Loads video for timer preview
   * @param {HTMLSelectElement} select - The timer video select element
   */
  function loadVideoForTimer(select) {
    const previewContainer = findChildNodeInParentTree(select, "[id^=preview-video-container-]");
    const selectedValue = select.selectedOptions?.[0]?.value;
    const uid = previewContainer?.getAttribute('data-uniq-id');
    
    if (!previewContainer || !selectedValue || !uid) {
      return;
    }
    
    const loader = document.querySelector('#preview-video-loader');
    const videoLoader = buildPreviewTemplate({ external_id: selectedValue, source: "fileboy" }, uid);
    
    if (videoLoader && loader) {
      loader.innerHTML = '';
      loader.appendChild(videoLoader);
      htmx.process(videoLoader);
      htmx.trigger(videoLoader, 'load_preview');
    }
  }

  // =============================================================================
  // MARK: - TOUR MANAGEMENT
  // =============================================================================

  /**
   * Synchronizes tour ID field with available videos
   * @param {HTMLSelectElement} node - The tour ID select element
   */
  async function handleSynchronizeTourIdField(node) {
    const autoFocusVideoIdNode = findChildNodeInParentTree(node, '[name="slide[autoFocusVideoId]"]');
    if (!autoFocusVideoIdNode) {
      return;
    }
    
    const tourId = node.value;
    if (!tourId) {
      return;
    }
    
    const params = new URLSearchParams({ tour_id: tourId });
    const response = await fetch(`<%= tour_videos_tours_path %>?${params.toString()}`, {
      method: "GET", 
      headers: { 'content-type': 'application/json' }
    });
    const result = await response.json();

    const clone = autoFocusVideoIdNode.cloneNode();
    clone.childNodes.forEach(node => node.remove());

    const default_opt = document.createElement("option");
    default_opt.value = '';
    default_opt.textContent = 'Select from tour videos';
    clone.appendChild(default_opt);
    
    for (option of result) {
      const new_opt = document.createElement("option");
      new_opt.value = option.value;
      new_opt.textContent = option.full_name;
      clone.appendChild(new_opt);
    }
    
    autoFocusVideoIdNode.replaceWith(clone);
    const selectedAutoFocus = "<%=  %>";
  }

  // =============================================================================
  // MARK: - QUIZ QUESTION MANAGEMENT
  // =============================================================================

  /**
   * Generates a new quiz question of the specified type
   * @param {HTMLButtonElement} buttonNode - The generate button element
   */
  async function handleGenerateQuizQuestionType(buttonNode) {
    const uid = buttonNode.getAttribute('data-uniq-id');
    if (!uid) {
      console.error("handleGenerateQuizQuestionType invoked by invalid caller missing uniq-id");
      return;
    }

    const parent = buttonNode.parentElement;
    const typeSelect = parent.querySelector('[name="slide[quiz_question_type]"]');
    if (!typeSelect) {
      console.log("Could not locate quiz_question_type select");
      return;
    }

    function addErrorStyles() {
      typeSelect.classList.add("border-red-500", "ring-2", "ring-red-300");
    }
    
    function removeErrorStyles() {
      typeSelect.classList.remove("border-red-500", "ring-2", "ring-red-300");
    }

    const selectedOption = typeSelect.selectedOptions[0];
    console.log("Selected");
    
    if (!selectedOption?.value) {
      console.error("Type select has no selected option");
      addErrorStyles();
      return;
    }
    
    removeErrorStyles();

    const questionIdField = findChildNodeInParentTree(parent, '[name="slide[quip_question_id]"]');
    if (!questionIdField) {
      console.error("Couldn't find the question ID field");
      return;
    }

    const url = "<%= generate_quiz_question_path.call(lesson_template) %>";
    const request = await fetch(url, {
      method: "POST",
      headers: { "content-type": "application/json" },
      body: JSON.stringify({ question_type: selectedOption.value })
    });
    
    if (!request.ok) {
      console.error("Error fetching url", request);
      return;
    }
    
    const body = await request.json();
    if (body.error) {
      console.error("Error in response", body);
      return;
    }

    const questionId = body?.question_id;
    if (!questionId) {
      console.error("Expected a question id but didn't receive one!?", body);
      return;
    }

    questionIdField.value = questionId;
    questionIdField.dispatchEvent(new Event('change', { bubbles: true }));

    const parentPanel = findChildNodeInParentTree(buttonNode, '#generate-quiz-panel');
    parentPanel.style.display = "none";

    handleLoadQuipQuizLinkForQuestion(questionIdField).then(href => {
      if (href) {
        // Safari blocks window.open in async code
        setTimeout(() => {
          window.open(href, '_blank');
        })
        
      }
      const form = buttonNode.closest('form');
      addParamToFormAction(form, 'reopen_draw', 'true');
      form.submit();
    });
  }

  /**
   * Loads quiz link for a specific question
   * @param {HTMLInputElement} quipQuestionIdField - The question ID input field
   * @returns {Promise<string>} - The quiz edit URL
   */
  async function handleLoadQuipQuizLinkForQuestion(quipQuestionIdField) {
    const questionID = quipQuestionIdField.value;
    const tableRow = quipQuestionIdField.closest("tr");
    const slideId = tableRow?.getAttribute('data-slide-id') || "";

    if (!questionID) {
      console.warn("quip_question_id field has no value");
      setQuizLinkNodeVisible(quipQuestionIdField, false);
      setGenerateQuizPanelVisible(quipQuestionIdField, true);
      return;
    }

    setGenerateQuizPanelVisible(quipQuestionIdField, false);

    console.log("Looking up quiz by question id", questionID, slideId);
    const url = "<%= question_details_path.call(lesson_template) %>";
    const params = new URLSearchParams({ question_id: questionID, slide_id: slideId });
    const request = await fetch(url + "?" + params.toString(), {
      method: "GET",
      headers: { "content-type": "application/json" },
    });
    
    if (!request.ok) {
      console.error("Error fetching url", request);
      return;
    }
    
    const resultData = await request.json();
    if (!resultData) {
      console.error("Question not found");
      setQuizLinkNodeVisible(quipQuestionIdField, false);
      setGenerateQuizPanelVisible(quipQuestionIdField, true);
      return;
    }

    const quizLinkNode = findChildNodeInParentTree(quipQuestionIdField, '#quip-question-id-link');
    quizLinkNode.innerHTML = '';

    if (resultData.type) {
      const info = document.createElement("div");

      const typeNode = document.createElement('p');
      typeNode.classList.add('font-bold', 'text-sm');
      typeNode.textContent = resultData.type;
      info.appendChild(typeNode);

      const textNode = document.createElement('p');
      textNode.textContent = resultData.prompt?.length > 250 ? resultData.prompt.slice(0, 250) + '...' : resultData.prompt;
      info.appendChild(textNode);

      if (resultData.count_references) {
        const referenceCount = document.createElement('p');
        const count = resultData.count_references;
        referenceCount.textContent = `${count} slide${count == 1 ? '' : 's'} reference this question`;
        referenceCount.classList.add('underline', 'text-sm');
        info.appendChild(referenceCount);
      }

      info.classList.add('p-2', 'rounded-md', 'bg-gray-100', 'mt-1', 'mb-1');
      quizLinkNode.appendChild(info);
    }

    let href = "";
    if (resultData.quip_quiz_id) {
      const link = document.createElement("a");
      href = `/quiz-builder/${resultData.quip_quiz_id}/edit?question_id=${resultData.question_id}&return_to_name=Editing%20<%= @lesson_template.name %>%20Presentation&return_to_url=${encodeURIComponent(window.location.pathname)}`;
      link.href = href;
      link.target = "_blank";
      link.textContent = 'View Quiz';
      link.classList.add(...("mt-2 inline-flex items-center px-2.5 py-1.5 border border-blue-300 text-sm font-medium rounded text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500".split(" ")));
      quizLinkNode.appendChild(link);
    }
    
    setQuizLinkNodeVisible(quipQuestionIdField, true);
    return href;
  }

  /**
   * Triggers loading of quiz link when drawer is opened
   * @param {HTMLElement} node - The drawer content node
   */
  async function triggerLoadLinkOnDrawOpen(node) {
    const questionIdField = node.querySelector('[name="slide[quip_question_id]"]');
    handleLoadQuipQuizLinkForQuestion(questionIdField);
  }

  // =============================================================================
  // MARK: - HOMEWORK TASK MANAGEMENT
  // =============================================================================

  /**
   * Loads homework tasks for a specific school
   * @param {HTMLSelectElement} select - The school select element
   */
  async function loadHomeworkTasks(select) {
    const selected = select.selectedOptions[0]?.value;
    if (!selected) {
      return;
    }
    
    const homeworkTaskSelect = findChildNodeInParentTree(select, "[name='slide[homework_task_id]']");
    if (!homeworkTaskSelect) {
      return;
    }
    
    const taskSelectContainer = findChildNodeInParentTree(select, "[name='task-select']");
    if (!taskSelectContainer) {
      return;
    }

    const url = "<%= school_lesson_editing_independent_learning_tasks_path(@lesson_template, "__ID__") %>".replace("__ID__", selected);

    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;
    if (!csrfToken) {
      console.error('CSRF token not found');
      return;
    }

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': csrfToken,
        'Accept': 'application/json'
      },
    });
    
    const result = await response.json();
    const clone = homeworkTaskSelect.cloneNode();
    clone.childNodes.forEach(node => node.remove());

    const currentSelectedOptId = homeworkTaskSelect.selectedOptions[0]?.value;

    const default_opt = document.createElement("option");
    default_opt.value = '';
    default_opt.textContent = 'Select from tasks';
    clone.appendChild(default_opt);
    
    for (option of result) {
      const new_opt = document.createElement("option");
      new_opt.value = option.value;
      new_opt.textContent = option.label;
      clone.appendChild(new_opt);
    }
    
    homeworkTaskSelect.replaceWith(clone);
    taskSelectContainer.classList.remove('hidden');
  }

  // =============================================================================
  // MARK: - AI GENERATION
  // =============================================================================

  /**
   * Confirms and triggers AI generation for slides
   */
  function confirmAiGeneration() {
    if (confirm("Are you sure you want to generate this slide using AI? This action cannot be undone.")) {
      const path = '<%= escape_javascript generate_with_ai_admin_lesson_template_path(@lesson_template) %>';
      window.location.href = path;
    }
  }

  // =============================================================================
  // MARK: - EVENT LISTENERS AND INITIALIZATION
  // =============================================================================

  /**
   * Initializes all event listeners and components when DOM is loaded
   */
  document.addEventListener('DOMContentLoaded', function() {
    // Check for slide_id parameter in URL and auto-open drawer
    const urlParams = new URLSearchParams(window.location.search);
    const slideId = urlParams.get('slide_id');
    
    if (slideId) {
      const slideRow = document.querySelector(`tr[data-slide-id="${slideId}"]`);
      
      if (slideRow) {
        const editTrigger = slideRow.querySelector('button[data-edit-button]');
        
        if (editTrigger) {
          setTimeout(() => {
            editTrigger.click();
            
            // Remove the slide_id parameter from URL after opening
            const newUrl = new URL(window.location);
            newUrl.searchParams.delete('slide_id');
            window.history.replaceState({}, '', newUrl);
          }, 100);
        } else {
          console.warn(`[autodrawopen] Edit button for slide ${slideId} not found`);
        }
      } else {
        console.warn(`[autodrawopen] Slide with ID ${slideId} not found`);
      }
    }

    // Initialize core functionality
    initSortable();
    preventDefaultDrag();
    initSlideTypeForms();
    initImageFieldValidator();

    // Initialize timer video selects
    const selectLists = document.querySelectorAll("[data-field-timer] [name='slide[timer_video_id]']");
    selectLists.forEach(select => select.addEventListener('change', () => {
      loadVideoForTimer(select);
    }));

    // Initialize slide type change handlers
    const slideTypeFields = document.querySelectorAll("[name='slide[slide_type]']");
    slideTypeFields.forEach(field => {
      field.addEventListener('change', () => {
        toggleLegacyVideoFields(field);
        setSlideTypeDescription(field);
      });
      setSlideTypeDescription(field);
    });
  });

  /**
   * Handles drawer opened events
   */
  document.addEventListener('drawer:opened', (event) => {
    const node = event.detail.target;
    
    // Handle video field preview
    const videoField = node.querySelector('.video-field');
    if (videoField) {
      handleVideoFieldPreview(videoField);
    }
    
    // Trigger quiz link loading
    triggerLoadLinkOnDrawOpen(node);

    // Handle timer field
    const timerField = node.querySelector('[data-field-timer]');
    if (timerField) {
      const select = timerField.querySelector("[name='slide[timer_video_id]']");
      loadVideoForTimer(select);
    }
  });

  // MARK: SLIDE PREVIEW
  function handleSlidePreview(button, slideId) {
    const dialog = document.querySelector("#slide-preview-dialog")
    const content = dialog.querySelector("#slide-preview-content")
    const route = `/presentation/slide/${slideId}`
    // clear the current data in the dialog
    content.innerHTML = '';

    // build a htmx loader to fetch the slide info
    const div = document.createElement('div');
    div.setAttribute('hx-get', route);
    div.setAttribute('hx-trigger', 'load_slide_preview');
    div.setAttribute('hx-target', 'this');
    div.setAttribute('hx-swap', 'outerHTML');
    div.setAttribute('form', 'none');    
    div.innerHTML = `
      <div class="flex items-center mx-auto gap-2">
        <svg class="w-6 h-6 text-gray-400 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.372 0 0 5.372 0 12h4z"></path>
        </svg>
        <span class="text-sm text-gray-600">Loading preview...</span>
      </div>
    `
    div.classList.add(..."flex items-center justify-center bg-white h-full w-full".split(" "))
    
    // add the div into the content, then trigger it to rebuild itself
    content.appendChild(div);
    htmx.process(div);
    htmx.trigger(div, 'load_slide_preview');
    dialog.showModal()
  }
  function closeSlidePreview() {
    const dialog = document.querySelector("#slide-preview-dialog")
    const content = dialog.querySelector("#slide-preview-content")
    dialog.close();
    content.innerHTML = '';
  }
  document.addEventListener("DOMContentLoaded", () => {
    const dialog = document.querySelector("#slide-preview-dialog")

    dialog.addEventListener('click', function(event) {
      if (event.target === dialog) {
        closeSlidePreview();
      }
    });
  })

  /**
   * Custom form validation for image fields that are required
   */
  function initImageFieldValidator() {
    const body = document.body;

    if (body.dataset.imageFieldValidatorAttached) {
      return;
    }
    body.dataset.imageFieldValidatorAttached = 'true';

    console.log("[ImageFieldValidator] Initialised");

    body.addEventListener('submit', function(event) {
      // Find submitted form
      const form = event.target.closest('form');
      if (!form) {
        return;
      }

      let imageFieldsAreValid = true;
      const submitButton = form.querySelector('input[type="submit"], button[type="submit"]');

      // Reset error style
      clearImageFieldValidationStates(form);

      // Find image fields with required: true
      const requiredImageFields = form.querySelectorAll('.image-field-container[data-required="true"]');

      // Check if required image fields contain an image
      requiredImageFields.forEach(imageFieldContainer => {
        const hiddenImageInput = imageFieldContainer.querySelector('input[type="text"]');
        if (hiddenImageInput && hiddenImageInput.value.trim() === '' && imageFieldContainer.offsetParent !== null) {
          imageFieldsAreValid = false;
          imageFieldContainer.classList.add('has-error', 'border-red-500');
        }
      });

      // Prevent form submission if required image is empty
      if (!imageFieldsAreValid) {
        event.preventDefault();
        
        // Enable submit button
        if (submitButton) {
          setTimeout(() => { submitButton.disabled = false; }, 100);
        }
        
        // Scroll to errored field
        const firstError = form.querySelector('.has-error');
        if (firstError) {
          firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }
    });
  }

  function clearImageFieldValidationStates(form) {
    form.querySelectorAll('.has-error').forEach(container => {
      container.classList.remove('has-error', 'border-red-500');
    });
  }
</script>
