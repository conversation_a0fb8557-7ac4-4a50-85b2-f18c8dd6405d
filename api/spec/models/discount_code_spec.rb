# frozen_string_literal: true

# == Schema Information
#
# Table name: discount_codes
#
#  id              :bigint           not null, primary key
#  active          :boolean          default(TRUE)
#  amount          :integer          not null
#  code            :string           not null
#  description     :string           not null
#  discount_type   :string           not null
#  expires_at      :datetime
#  max_redemptions :integer
#  metadata        :jsonb
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#
# Indexes
#
#  index_discount_codes_on_code  (code) UNIQUE
#
require 'rails_helper'

RSpec.describe DiscountCode, type: :model do
  describe 'validations' do
    # Use the skip_stripe_callbacks trait to avoid Stripe API calls during validation tests
    it 'is valid with valid attributes' do
      discount_code = build(:discount_code, :skip_stripe_callbacks)
      expect(discount_code).to be_valid
    end

    it 'requires a code' do
      discount_code = build(:discount_code, :skip_stripe_callbacks, code: nil)
      expect(discount_code).not_to be_valid
      expect(discount_code.errors[:code]).to include("can't be blank")
    end

    it 'requires a unique code' do
      create(:discount_code, :skip_stripe_callbacks, code: 'TESTCODE')
      duplicate_code = build(:discount_code, :skip_stripe_callbacks, code: 'TESTCODE')
      expect(duplicate_code).not_to be_valid
      expect(duplicate_code.errors[:code]).to include('has already been taken')
    end

    it 'requires a discount_type' do
      discount_code = build(:discount_code, :skip_stripe_callbacks, discount_type: nil)
      expect(discount_code).not_to be_valid
      expect(discount_code.errors[:discount_type]).to include("can't be blank")
    end

    it 'requires discount_type to be one of the allowed values' do
      discount_code = build(:discount_code, :skip_stripe_callbacks, discount_type: 'invalid_type')
      expect(discount_code).not_to be_valid
      expect(discount_code.errors[:discount_type]).to include('is not included in the list')
    end

    it 'requires an amount' do
      discount_code = build(:discount_code, :skip_stripe_callbacks, amount: nil)
      expect(discount_code).not_to be_valid
      expect(discount_code.errors[:amount]).to include("can't be blank")
    end

    it 'requires amount to be greater than 0' do
      discount_code = build(:discount_code, :skip_stripe_callbacks, amount: 0)
      expect(discount_code).not_to be_valid
      expect(discount_code.errors[:amount]).to include('must be greater than 0')
    end

    it 'validates percentage amount is between 1 and 100' do
      discount_code = build(:discount_code, :skip_stripe_callbacks, discount_type: 'percentage', amount: 101)
      expect(discount_code).not_to be_valid
      expect(discount_code.errors[:amount]).to include('must be between 1 and 100 for percentage discounts')
    end

    it 'requires a description' do
      discount_code = build(:discount_code, :skip_stripe_callbacks, description: nil)
      expect(discount_code).not_to be_valid
      expect(discount_code.errors[:description]).to include("can't be blank")
    end

    it 'validates max_redemptions is a non-negative integer' do
      discount_code = build(:discount_code, :skip_stripe_callbacks, max_redemptions: -1)
      expect(discount_code).not_to be_valid
      expect(discount_code.errors[:max_redemptions]).to include('must be greater than or equal to 0')
    end
  end

  describe 'scopes' do
    before do
      @active_unexpired = create(:discount_code, :skip_stripe_callbacks, active: true, expires_at: 1.month.from_now)
      @active_expired = create(:discount_code, :skip_stripe_callbacks, active: true, expires_at: 1.day.ago)
      @inactive_unexpired = create(:discount_code, :skip_stripe_callbacks, active: false, expires_at: 1.month.from_now)
      @inactive_expired = create(:discount_code, :skip_stripe_callbacks, active: false, expires_at: 1.day.ago)
      @active_no_expiry = create(:discount_code, :skip_stripe_callbacks, active: true, expires_at: nil)
    end

    it 'active scope returns only active codes' do
      expect(described_class.active).to include(@active_unexpired, @active_expired, @active_no_expiry)
      expect(described_class.active).not_to include(@inactive_unexpired, @inactive_expired)
    end

    it 'not_expired scope returns codes that are not expired' do
      expect(described_class.not_expired).to include(@active_unexpired, @inactive_unexpired, @active_no_expiry)
      expect(described_class.not_expired).not_to include(@active_expired, @inactive_expired)
    end

    it 'available scope returns active and not expired codes' do
      expect(described_class.available).to include(@active_unexpired, @active_no_expiry)
      expect(described_class.available).not_to include(@active_expired, @inactive_unexpired, @inactive_expired)
    end
  end

  describe 'callback behaviors' do
    it 'converts code to uppercase before saving' do
      discount_code = create(:discount_code, :skip_stripe_callbacks, code: 'lowercasecode')
      expect(discount_code.reload.code).to eq('LOWERCASECODE')
    end
  end

  describe 'instance methods' do
    describe '#redeemable?' do
      it 'returns true if code is active, not expired, and has redemptions available' do
        discount_code = create(:discount_code, :skip_stripe_callbacks, active: true, expires_at: 1.week.from_now, max_redemptions: 10)
        expect(discount_code.redeemable?).to be true
      end

      it 'returns false if code is inactive' do
        discount_code = create(:discount_code, :skip_stripe_callbacks, active: false, expires_at: 1.week.from_now)
        expect(discount_code.redeemable?).to be false
      end

      it 'returns false if code is expired' do
        discount_code = create(:discount_code, :skip_stripe_callbacks, active: true, expires_at: 1.day.ago)
        expect(discount_code.redeemable?).to be false
      end

      it 'returns false if max redemptions reached' do
        discount_code = create(:discount_code, :skip_stripe_callbacks, active: true, expires_at: 1.week.from_now, max_redemptions: 2)
        subscriber1 = create(:subscriber)
        subscriber2 = create(:subscriber)

        # Create two redemptions
        create(:discount_code_redemption, discount_code: discount_code, subscriber: subscriber1)
        create(:discount_code_redemption, discount_code: discount_code, subscriber: subscriber2)

        expect(discount_code.redeemable?).to be false
      end
    end

    describe '#expired?' do
      it 'returns true if expiration date is in the past' do
        discount_code = create(:discount_code, :skip_stripe_callbacks, expires_at: 1.day.ago)
        expect(discount_code.expired?).to be true
      end

      it 'returns false if expiration date is in the future' do
        discount_code = create(:discount_code, :skip_stripe_callbacks, expires_at: 1.day.from_now)
        expect(discount_code.expired?).to be false
      end

      it 'returns false if no expiration date is set' do
        discount_code = create(:discount_code, :skip_stripe_callbacks, expires_at: nil)
        expect(discount_code.expired?).to be false
      end
    end

    describe '#redemptions_exhausted?' do
      it 'returns true if max redemptions reached' do
        discount_code = create(:discount_code, :skip_stripe_callbacks, max_redemptions: 2)
        subscriber1 = create(:subscriber)
        subscriber2 = create(:subscriber)

        # Create two redemptions
        create(:discount_code_redemption, discount_code: discount_code, subscriber: subscriber1)
        create(:discount_code_redemption, discount_code: discount_code, subscriber: subscriber2)

        expect(discount_code.redemptions_exhausted?).to be true
      end

      it 'returns false if max redemptions not reached' do
        discount_code = create(:discount_code, :skip_stripe_callbacks, max_redemptions: 5)
        subscriber = create(:subscriber)

        # Create one redemption
        create(:discount_code_redemption, discount_code: discount_code, subscriber: subscriber)

        expect(discount_code.redemptions_exhausted?).to be false
      end

      it 'returns false if max redemptions is nil (unlimited)' do
        discount_code = create(:discount_code, :skip_stripe_callbacks, max_redemptions: nil)
        subscriber = create(:subscriber)

        # Create a redemption
        create(:discount_code_redemption, discount_code: discount_code, subscriber: subscriber)

        expect(discount_code.redemptions_exhausted?).to be false
      end
    end

    describe '#redeem!' do
      let(:discount_code) { create(:discount_code, :skip_stripe_callbacks, active: true, expires_at: 1.month.from_now, max_redemptions: 3) }
      let(:subscriber) { create(:subscriber) }

      it 'creates a redemption record and returns true if successful' do
        expect do
          result = discount_code.redeem!(subscriber)
          expect(result).to be true
        end.to change(DiscountCodeRedemption, :count).by(1)

        redemption = DiscountCodeRedemption.last
        expect(redemption.discount_code).to eq(discount_code)
        expect(redemption.subscriber).to eq(subscriber)
      end

      it 'deactivates the code if max redemptions reached' do
        discount_code.update(max_redemptions: 1)

        expect do
          discount_code.redeem!(subscriber)
        end.to change { discount_code.reload.active? }.from(true).to(false)
      end

      it 'returns false if code cannot be redeemed' do
        # Make the code not redeemable
        discount_code.update(active: false)

        expect do
          result = discount_code.redeem!(subscriber)
          expect(result).to be false
        end.not_to change(DiscountCodeRedemption, :count)
      end

      it 'raises an error if subscriber has already redeemed the code' do
        # First redemption
        discount_code.redeem!(subscriber)
        initial_count = DiscountCodeRedemption.count

        # Try to redeem again and expect an exception
        expect { discount_code.redeem!(subscriber) }.to raise_error(ActiveRecord::RecordInvalid)
        expect(DiscountCodeRedemption.count).to eq(initial_count)
      end
    end

    describe '#formatted_discount' do
      it 'formats percentage discounts with % symbol' do
        discount_code = build(:discount_code, :skip_stripe_callbacks, discount_type: 'percentage', amount: 25)
        expect(discount_code.formatted_discount).to eq('25%')
      end

      it 'formats fixed amount discounts with £ symbol' do
        discount_code = build(:discount_code, :skip_stripe_callbacks, discount_type: 'fixed_amount', amount: 1000)
        expect(discount_code.formatted_discount).to eq('£10.00')
      end
    end
  end

  describe 'Stripe integration' do
    let(:discount_code) { build(:discount_code, code: 'TEST25', discount_type: 'percentage', amount: 25, description: 'Test Discount') }

    describe '#stripe_coupon_id' do
      it 'generates a Stripe coupon ID based on the code' do
        expect(discount_code.stripe_coupon_id).to eq('COUPON_TEST25')
      end
    end

    describe 'Stripe API interactions', :vcr do
      before do
        # Create a stub for Stripe::Coupon
        allow(Stripe::Coupon).to receive(:create).and_return(double('stripe_coupon', id: 'COUPON_TEST25'))
        allow(Stripe::Coupon).to receive(:retrieve).and_return(double('stripe_coupon', id: 'COUPON_TEST25'))
        allow(Stripe::Coupon).to receive(:update).and_return(double('stripe_coupon', id: 'COUPON_TEST25'))
        allow(Stripe::Coupon).to receive(:delete).and_return(double('deleted_coupon'))

        # Stub Rails logger to prevent test output noise
        allow(Rails.logger).to receive(:info)
        allow(Rails.logger).to receive(:error)

        # Ensure the callbacks are invoked for these tests specifically
        # We'll override the :skip_stripe_callbacks trait for these tests
        allow_any_instance_of(described_class).to receive(:create_in_stripe).and_call_original
        allow_any_instance_of(described_class).to receive(:update_in_stripe).and_call_original
        allow_any_instance_of(described_class).to receive(:delete_in_stripe).and_call_original
      end

      it 'creates a Stripe coupon after creation' do
        expect(Stripe::Coupon).to receive(:create).with(
          hash_including(
            id: 'COUPON_TEST25',
            name: 'Test Discount',
            percent_off: 25
          )
        )

        discount_code.save!
      end

      it 'creates a fixed amount coupon for fixed_amount type' do
        discount_code.discount_type = 'fixed_amount'
        discount_code.amount = 1000

        expect(Stripe::Coupon).to receive(:create).with(
          hash_including(
            id: 'COUPON_TEST25',
            amount_off: 1000,
            currency: 'gbp'
          )
        )

        discount_code.save!
      end

      it 'updates the Stripe coupon metadata when updated' do
        # First save to create
        discount_code.save!

        # Then update
        expect(Stripe::Coupon).to receive(:update).with(
          'COUPON_TEST25',
          hash_including(
            name: 'Updated Description',
            metadata: hash_including(
              discount_code_id: discount_code.id
            )
          )
        )

        discount_code.update!(description: 'Updated Description')
      end

      it 'deletes the Stripe coupon when destroyed' do
        discount_code.save!

        expect(Stripe::Coupon).to receive(:delete).with('COUPON_TEST25')

        discount_code.destroy
      end

      it 'handles Stripe errors gracefully during create' do
        allow(Stripe::Coupon).to receive(:create).and_raise(Stripe::StripeError.new('Stripe API error'))

        # Should not raise the error
        expect { discount_code.save! }.to raise_error(Stripe::StripeError)

        # Should log the error
        expect(Rails.logger).to have_received(:error).with(/Error creating Stripe coupon/)
      end

      it 'handles Stripe errors gracefully during update' do
        discount_code.save!

        allow(Stripe::Coupon).to receive(:update).and_raise(Stripe::StripeError.new('Stripe API error'))

        # Should not raise the error
        expect { discount_code.update!(description: 'New description') }.not_to raise_error

        # Should log the error
        expect(Rails.logger).to have_received(:error).with(/Error updating Stripe coupon/)
      end

      it 'handles Stripe errors gracefully during delete' do
        discount_code.save!

        allow(Stripe::Coupon).to receive(:delete).and_raise(Stripe::StripeError.new('Stripe API error'))

        # Should not raise the error
        expect { discount_code.destroy }.not_to raise_error

        # Should log the error
        expect(Rails.logger).to have_received(:error).with(/Error deleting Stripe coupon/)
      end
    end
  end
end
