const defaultTheme = require("tailwindcss/defaultTheme");

module.exports = {
    darkMode: "class",
    content: [
        "./public/*.html",
        "./app/helpers/**/*.rb",
        "./app/javascript/**/*.js",
        "./app/views/**/*.{erb,haml,html,slim}",
        "./app/components/**/*.{erb,html}"
    ],
    safelist: [
        "bg-orange-600",
        "bg-red-500",
        "bg-contain",
        "bg-red-100",
        "whitespace-pre-wrap",
        "object-contain",
        "object-cover",
        "bg-green-200", 
        "bg-red-200",
        "from-emerald-50",
        "to-emerald-100",
        "from-orange-50",
        "to-orange-100",
        "from-purple-900",
        "via-indigo-800",
        "to-blue-800",
        "from-green-900",
        "via-emerald-800",
        "to-teal-700",
        "from-cyan-600",
        "via-blue-600",
        "to-indigo-700",
        "from-purple-600",
        "via-violet-600",
        "from-emerald-600",
        "via-teal-600",
        "to-green-700",
        "from-orange-600",
        "via-amber-500",
        "to-red-600",
        "from-slate-600",
        "via-gray-600",
        "to-zinc-700",
        "from-teal-600",
        "via-emerald-600", 
        "to-cyan-600",
        "from-amber-600",
        "to-yellow-600"
    ],
    theme: {
        extend: {
            dropShadow: {
                "outline-black": "0 1px 2px rgba(0, 0, 0, 1)"
            },
            fontFamily: {
                sans: ["proxima-soft", ...defaultTheme.fontFamily.sans],
                headings: ["proxima-nova", ...defaultTheme.fontFamily.sans]
            },
            gridTemplateColumns: {
                "fluid-12": "repeat(auto-fit, minmax(12rem, 1fr))",
                "fluid-14": "repeat(auto-fit, minmax(14rem, 1fr))",
                "fluid-18": "repeat(auto-fit, minmax(18rem, 1fr))",
                "fluid-24": "repeat(auto-fit, minmax(20rem, 1fr))",
                "fill-16": "repeat(auto-fill, minmax(16rem, 1fr))",
                "fill-20": "repeat(auto-fill, minmax(20rem, 1fr))",
                "3-1": "3fr 1fr"
            },
            colors: {
                "mid-blue": "#3A5CC8",
                "dark-blue": "#130E3C",
                "secondary-dark-blue": "#282256",
                "light-blue": "#B5DEF8",
                "de-blue": "#3AB2AF",
                "de-brand": "#33c6c6",
                "de-red": "#FF5A5A",
                "de-pink": "#f4b3dd",
                "de-yellow": "#FFCD00"
            },
            flex: {
                2: "2 2 0%",
                3: "3 3 0%"
            },
            typography: {
                DEFAULT: {
                    a: {
                        color: "#33C6C6",
                        "&:hover": {
                            color: "#68EFEFFF"
                        }
                    }
                }
            }
        }
    },
    plugins: [
        require("@tailwindcss/forms"),
        require("@tailwindcss/aspect-ratio"),
        require("@tailwindcss/typography"),
        require("@tailwindcss/container-queries")
    ]
};
